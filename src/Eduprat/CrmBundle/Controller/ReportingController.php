<?php

namespace Eduprat\CrmBundle\Controller;

use Symfony\Component\HttpFoundation\Response;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\ParticipantSearch;
use Eduprat\CrmBundle\Services\ReportingService;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\FinanceMode;
use Eduprat\DomainBundle\Services\SearchHandler;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route(path: '/reporting')]
class ReportingController extends EdupratController
{
    /**
     * @param ReportingService $reporting
     * @return Response
     */
    #[Route(path: '/actions', name: 'crm_reporting_actions')]
    public function actions(ReportingService $reporting): Response
    {
        $currentYear = (int) (new \DateTime())->format("Y");
        $years = range($currentYear, $currentYear - 1);
        $generateYearReport = $reporting->generateYearReport($years);
//        VarDumper::dump($generateYearReport);die;
        return $this->render('crm/reporting/actions.html.twig', array(
            'reporting' => $generateYearReport,
            'years' => $years
        ));
    }

    /**
     * @param Request $request
     * @param EntityManagerInterface $em
     * @param ReportingService $reporting
     * @param SearchHandler $searchHandler
     * @return Response
     */
    #[Route(path: '/participants', name: 'crm_reporting_participants')]
    public function participants(Request $request, EntityManagerInterface $em, ReportingService $reporting, SearchHandler $searchHandler)
    {
        $currentYear = (int) (new \DateTime())->format("Y");
        $years = range($currentYear, $currentYear - 1);

        $search = new ParticipantSearch();

        if ($request->query->get("financeMode")) {
            $search->financeMode = $em->getRepository(FinanceMode::class)->find($request->query->get("financeMode"));
        }

        $form = $this->createFormBuilder($search)
            ->add('financeMode', EntityType::class, array(
            'class' => FinanceMode::class,
            'label'    => 'admin.programme.financeMode',
            'choice_label' => function($financeMode) {
                return $financeMode->getName();
            },
            'expanded' => false,
            'multiple' => false,
            'required' => false,
            'placeholder' => 'admin.global.all',
        ))->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            return $this->redirectToRoute('crm_reporting_participants', $search->getParams());
        }

        $participantsReport = $reporting->generateParticipantsReport($years);

        return $this->render('crm/reporting/participants.html.twig', array(
            'reporting' => $participantsReport,
            'years' => $years,
            'form' => $form,
            'search' => $search,
        ));
    }

    /**
     * @param Request $request
     * @param EntityManagerInterface $em
     * @param ReportingService $reporting
     * @param SearchHandler $searchHandler
     * @return Response
     */
    #[Route(path: '/participants-repartition/{financeMode}', name: 'crm_reporting_participants_repartition')]
    public function participantsRepartition(EntityManagerInterface $em, ReportingService $reporting, FinanceMode $financeMode = null): Response
    {
        $currentYear = (int) (new \DateTime())->format("Y");
        $years = range($currentYear, $currentYear - 1);

        $search = new ParticipantSearch();

        if ($financeMode) {
            $search->financeMode = $financeMode;
        }

        $participantsReport = $reporting->generateParticipantsReportCategories($search, $years);

        return $this->render('crm/reporting/repartition.html.twig', array(
            'reporting' => $participantsReport,
            'years' => $years,
            'search' => $search,
        ));
    }

}
