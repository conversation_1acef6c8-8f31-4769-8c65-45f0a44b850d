<?php

namespace Eduprat\CrmBundle\Repository;

use Eduprat\CrmBundle\Entity\AnalysisConfig;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Eduprat\DomainBundle\Entity\Participant;
use Eduprat\DomainBundle\Entity\Participation;

/**
 * @method AnalysisConfig|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnalysisConfig|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnalysisConfig[]    findAll()
 * @method AnalysisConfig[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnalysisConfigRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnalysisConfig::class);
    }

    public function findObjectivesByCategory($year, $field = "ca") {
        $start = ($year + 1) . "-01-01";

        $q = $this->createQueryBuilder('ac')
            ->select('pa.exerciseMode, p.category, p.uga, SUM(ac.' . $field . ') AS objective')
            ->innerJoin(Participant::class, 'p', 'WITH', 'p.category = ac.category')
            ->leftJoin("p.participations", "pa", "with", "p.id = pa.participant AND pa.id = (SELECT MAX(pa2.id) FROM ".Participation::class." as pa2 WHERE pa2.participant = p.id and pa.archived = false)")
            ->andWhere("p.createdAt < :start")
            ->setParameter("start", $start)
            ->andWhere("ac.year = :year")
            ->setParameter("year", $year)
            ->addGroupBy("p.category")
            ->addGroupBy("p.uga")
            ->addGroupBy("pa.exerciseMode")
        ;
            
        return $q->getQuery()
            ->getScalarResult();
    }

    public function findValueByCategory($year, $field = "price") {
        $start = "$year-01-01";
        $end = "$year-12-31";

        return $this->createQueryBuilder('ac')
            ->select('f.id, f.startDate, pa.exerciseMode, p.category, pa.uga, SUM(pa.' . $field . ') AS value')
            ->innerJoin(Participant::class, 'p', 'WITH', 'p.category = ac.category')
            ->innerJoin("p.participations", "pa")
            ->innerJoin("pa.formation", "f")
            ->andWhere("f.startDate between :start and :end")
            ->setParameter('start', $start)
            ->setParameter('end', $end)
            ->andWhere("ac.year = :year")
            ->andWhere("pa.archived = false")
            ->setParameter("year", $year)
            ->addGroupBy("f.id")
            ->addGroupBy("p.category")
            ->addGroupBy("pa.uga")
            ->addGroupBy("pa.exerciseMode")
            ->getQuery()
            ->getScalarResult();
    }

}
