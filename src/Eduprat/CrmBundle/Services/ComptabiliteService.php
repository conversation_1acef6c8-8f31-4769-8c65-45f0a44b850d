<?php

namespace Eduprat\CrmBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\AuditBundle\Services\CourseManager;
use Eduprat\CrmBundle\Model\AttestationComptabiliteSearch;
use Eduprat\CrmBundle\Model\ComptabiliteSearch;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\ParticipationHistory;

class ComptabiliteService {

    /**
     * @var EntityManagerInterface
     */
    private EntityManagerInterface $entityManager;
    private string $parcoursV3MigrationDate;

    public function __construct(EntityManagerInterface $entityManager, $parcoursV3MigrationDate) {
        $this->entityManager = $entityManager;
        $this->parcoursV3MigrationDate = $parcoursV3MigrationDate;
    }

    public function generateReport(ComptabiliteSearch $search, $page = 1, $nbPerPage = 100)
    {
        return array(
            "participations" => $this->getMissingParticipationModules($search, $page, $nbPerPage),
        );
    }

    public function generateFilesReport(ComptabiliteSearch $search, $page = 1, $nbPerPage = 10)
    {
        return array(
            "files" => $this->getFiles($search, $page, $nbPerPage),
        );
    }
    public function generateAttestationFilesReport(AttestationComptabiliteSearch $search, $page = 1, $nbPerPage = 10)
    {
        return array(
            "files" => $this->getAttestationFiles($search, $page, $nbPerPage),
        );
    }

    public function getMissingParticipationForms(ComptabiliteSearch $search, $page = 1, $nbPerPage = 100)
    {
        $repo = $this->entityManager->getRepository(Participation::class);
        return $repo->findMissingForm($search, $page, $nbPerPage);
    }

    public function getMissingParticipationModules(ComptabiliteSearch $search, $page = 1, $nbPerPage = null, $isCsv = false)
    {
        $repo = $this->entityManager->getRepository(Participation::class);
        $offset = 0;
        if (!is_null($nbPerPage)) {
            $offset = ($page - 1) * $nbPerPage;
            $offset = max($offset, 0);
        }

        $groupedResult = $repo->findMissingModules($search, $offset, $nbPerPage);

        if ($isCsv) {
            return $groupedResult;
        }

        return array(
            "count" => $repo->countMissingModules($search),
            "result" => $groupedResult,
        );
    }

    public function findAllInscriptions(ComptabiliteSearch $search)
    {
        $repo = $this->entityManager->getRepository(ParticipationHistory::class);
        return $repo->findAllInscriptions($search);
    }

    public function getSearchInscriptions(ComptabiliteSearch $search, $page, $nbPerPage)
    {
        $repo = $this->entityManager->getRepository(ParticipationHistory::class);
        return $repo->findSearchInscriptions($search, $page, $nbPerPage);
    }

    public function getCountInscriptions(ComptabiliteSearch $search)
    {
        $repo = $this->entityManager->getRepository(ParticipationHistory::class);
        return $repo->countSearchInscriptionsResults($search);
    }
    

    public function getFiles(ComptabiliteSearch $search, $page = 1, $nbPerPage = 10): array
    {
        $repo = $this->entityManager->getRepository(Formation::class);

        return array(
            "count" => $repo->countMissingFiles($search),
            "result" => $repo->findMissingFiles($search, $page, $nbPerPage),
        );
    }

    public function getAttestationFiles(AttestationComptabiliteSearch $search, $page = 1, $nbPerPage = 10): array
    {
        $repo = $this->entityManager->getRepository(Participation::class);

        $results = $repo->getMissingAttestationFilesQuery($search,  $this->parcoursV3MigrationDate)->getQuery()->getResult();
        $resultCount = count($results);
        $totalPages = ceil($resultCount / $nbPerPage);
        $pagesContent = array_fill_keys(range(1, $totalPages), []);

        $reorder = [];
        foreach ($results as $participation) {
            $formation = $participation->getFormation();
            $echeance = $participation->getAttestationHonneur() ? intval($formation->getAttestationEcheanceN1()->format('Ymd')) : intval($formation->getAttestationEcheanceN()->format('Ymd'));
            if (isset($reorder[$echeance])) {
                array_push($reorder[$echeance], $participation);
            } else {
                $reorder[$echeance] = [$participation];
            }
        }
        ksort($reorder);

        $pageCounter = 1;
        foreach($reorder as $echeance) {
            foreach($echeance as $participation) {
                if (count($pagesContent[$pageCounter]) === $nbPerPage) {
                    $pageCounter++;
                }
                $pagesContent[$pageCounter][] = $participation;
            }
        }
        
        return array(
            "count" => $resultCount,
            "result" => $pagesContent[$page],
        );
    }

}
