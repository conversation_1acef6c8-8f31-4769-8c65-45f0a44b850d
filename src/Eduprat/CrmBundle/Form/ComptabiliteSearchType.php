<?php

namespace Eduprat\CrmBundle\Form;

use Alienor\FormBundle\Form\AbstractBaseType;

use Ed<PERSON>rat\CrmBundle\Model\ComptabiliteSearch;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Doctrine\ORM\EntityRepository;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Participant;

class ComptabiliteSearchType extends AbstractBaseType {

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        if ($options['displayQuerySearchDate']) {
            $builder
                ->add('querySearchStartDate', DateTimeType::class, array(
                    'format'   => 'dd/MM/yyyy',
                    'widget'   => 'single_text',
                    'html5'    => false,
                    'attr'     => ['provider' => 'datepicker', 'class' => 'datepicker form-control'],
                    'label'    => $options['displayStartDate'] ? 'admin.formation.search.start' : 'admin.global.dateDu',
                    'required' => false,
                ))
                ->add('querySearchEndDate', DateTimeType::class, array(
                    'format'   => 'dd/MM/yyyy',
                    'widget'   => 'single_text',
                    'html5'    => false,
                    'attr'     => ['provider' => 'datepicker', 'class' => 'datepicker form-control'],
                    'label'    => $options['displayStartDate'] ? 'admin.formation.search.end' : 'admin.global.dateAu',
                    'required' => false,
                ));
        }
        if ($options['displayDateInscription']) {
            $builder
                ->add('startInscription', DateTimeType::class, array(
                    'format' => 'dd/MM/yyyy',
                    'widget' => 'single_text',
                    'html5' => false,
                    'attr' => ['provider' => 'datepicker', 'class' => 'datepicker form-control'],
                    'label' => $options['displayStartDate'] ? 'admin.formation.search.start' : 'admin.global.dateDu',
                    'required' => false,
                ))
                ->add('endInscription', DateTimeType::class, array(
                    'format' => 'dd/MM/yyyy',
                    'widget' => 'single_text',
                    'html5' => false,
                    'attr' => ['provider' => 'datepicker', 'class' => 'datepicker form-control'],
                    'label' => $options['displayStartDate'] ? 'admin.formation.search.end' : 'admin.global.dateAu',
                    'required' => false,
                ));
        }

        if($options['displayStartDate'] == false ) {
            $builder->add('type', ChoiceType::class, array(
                'required' => false,
                'label' => 'admin.participant.type',
                'choices'  => $this->types(),
                'placeholder' => 'admin.global.all',
            ));
        }

        if($options['displayCoordinatorFilter']) {
            $builder->add('coordinator', EntityType::class, array(
                'class'         => Person::class,
                'label'         => 'admin.participant.coordinator',
                'choice_label'  => function ($person) {
                    return $person->getLastname() . ' ' . $person->getFirstname();
                },
                'expanded'      => false,
                'multiple'      => false,
                'required'      => false,
                'placeholder'   => 'admin.global.all',
                'query_builder' => function (EntityRepository $er) use ($options) {
                    $qb = $er->createQueryBuilder('p')
                        ->where('p.roles LIKE :roles')
                        ->andWhere("p.isArchived = false")
                        ->setParameter('roles', '%"ROLE_COORDINATOR"%')
                        ->orderBy('p.lastname', 'ASC');

                    if ($options["supervisorId"]) {
                        $qb->andWhere("p.supervisor = :supervisor")->setParameter("supervisor", $options["supervisorId"]);
                    }

                    return $qb;
                },
            ));
        }
        if($options['displayWebmasterFilter']) {
            $builder->add('webmaster', EntityType::class, array(
                'class' => Person::class,
                'label'    => 'admin.user.webmaster.title',
                'choice_label' => function($person) {
                    return $person->getLastname().' '.$person->getFirstname();
                },
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'placeholder' => 'admin.global.all',
                'query_builder' => function (EntityRepository $er) use ($options) {
                    $qb = $er->createQueryBuilder('p')
                        ->where('p.roles LIKE :roles')
                        ->innerJoin("p.coordinatorsPersonWebmaster", "c")
                        ->setParameter('roles', '%"ROLE_WEBMASTER"%')
                        ->orderBy('p.lastname', 'ASC')
                    ;

                    return $qb;
                },
            ));
        }

        if($options['displayPartenariat']) {
            $builder->add('partenariat', ChoiceType::class, array(
                'label' => 'admin.participant.partenariat',
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'choices'  => self::getPartenariats(),
                'placeholder' => 'admin.global.select',
            ));
        }

        $builder->add('parcoursMandatory', ChoiceType::class, array(
            'required' => false,
            'label' => 'admin.participant.parcoursMandatory',
            'choices'  => $this->parcoursChoices(),
            'placeholder' => 'admin.global.all',
        ));
    }

    public static function getPartenariats() : array
    {
        $partenariats = array (
            Participant::PART_GPM,
            Participant::PART_MSOIGNER,
            Participant::PART_MFM,
            Participant::PART_PODOLOGUE,
            Participant::PART_SITE_INTERNET,
            Participant::PART_LBI,
            Participant::PART_PHARMAZON
        );
        return array_combine($partenariats, $partenariats);
    }

    public function configureOptions(OptionsResolver $resolver): void {
        $resolver->setDefaults(array(
            'data_class' => ComptabiliteSearch::class,
            'displayCoordinatorFilter' => false,
            'displayWebmasterFilter' => false,
            'displayPartenariat' => false,
            'displayStartDate' => false,
            'supervisorId' => null,
            'displayQuerySearchDate' => true,
            'displayDateInscription' => false,
        ));
    }

    public function getBlockPrefix(): string {
        return 'eduprat_comptabilite_search';
    }

    public function parcoursChoices() {
        return array(
            "Parcours obligatoire" => "oui",
            "Parcours non obligatoire" => "non"
        );
    }

    public function types() {
        return array(
            "Inscriptions" => "Inscription",
            "Desinscriptions" => "Désinscription"
        );
    }

}
