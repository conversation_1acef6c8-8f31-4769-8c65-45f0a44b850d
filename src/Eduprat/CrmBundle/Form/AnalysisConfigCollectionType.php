<?php

namespace Eduprat\CrmBundle\Form;

use Alienor\FormBundle\Form\AbstractBaseType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class AnalysisConfigCollectionType extends AbstractBaseType {

    public function buildForm(FormBuilderInterface $builder, array $options): void {

        $builder->add('configs', CollectionType::class, array(
            'label' => false,
            'entry_type' => AnalysisConfigType::class,
        ));
    }

    public function configureOptions(OptionsResolver $resolver): void {
        $resolver->setDefaults(array(
            'data_class' => null,
        ));
    }

    public function getBlockPrefix(): string {
        return 'eduprat_analysis_config_collection';
    }

}
