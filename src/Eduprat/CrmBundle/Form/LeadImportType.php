<?php

namespace Eduprat\CrmBundle\Form;

use Alienor\FormBundle\Form\AbstractBaseType;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class LeadImportType extends AbstractBaseType {

    /**
     * @var array|null
     */
    private $ugas;

    public function __construct($ugas)
    {
        $this->ugas = $ugas;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void {

        if ($options["preview"]["clients"]) {
            foreach ($options["preview"]["clients"] as $index => $item) {
                $leadForm = $builder->create($index, FormType::class, [
                    "inherit_data" => true
                ]);

//                foreach ($item["participants"] as $participant) {
//                    $leadForm->add();
//                }

                $builder->add(
                    $leadForm
                );
            }
        }

    }

    /**
     * @return array
     */
    public function getUgas()
    {
        $ugas = array();

        foreach ($this->ugas as $index => $uga) {
            $ugas[$uga["label"]] = $uga["id"];
        }

        ksort($ugas);

        return $ugas;
    }

    public function configureOptions(OptionsResolver $resolver): void {
        $resolver->setDefaults(array(
            'data_class' => null,
            'preview' => array()
        ));
    }

    public function getBlockPrefix(): string {
        return 'eduprat_lead_import';
    }

}
