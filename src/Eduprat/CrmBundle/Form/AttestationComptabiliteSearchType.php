<?php

namespace Eduprat\CrmBundle\Form;

use Alienor\FormBundle\Form\AbstractBaseType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Doctrine\ORM\EntityRepository;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\CrmBundle\Model\AttestationComptabiliteSearch;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;

class AttestationComptabiliteSearchType extends AbstractBaseType {

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        if($options['displayStartDate']) {
            $builder
                ->add('querySearchStartDate', DateTimeType::class, array(
                    'format'   => 'dd/MM/yyyy',
                    'widget'   => 'single_text',
                    'html5'    => false,
                    'attr'     => ['provider' => 'datepicker', 'class' => 'datepicker form-control'],
                    'label'    => 'admin.formation.search.start',
                    'required' => false,
                ))
                ->add('querySearchEndDate', DateTimeType::class, array(
                    'format'   => 'dd/MM/yyyy',
                    'widget'   => 'single_text',
                    'html5'    => false,
                    'attr'     => ['provider' => 'datepicker', 'class' => 'datepicker form-control'],
                    'label'    => 'admin.formation.search.end',
                    'required' => false,
                ))
                ->add('courseEnded', ChoiceType::class, array(
                    'label'    => 'admin.participation.search.courseEnded',
                    "placeholder" => "admin.global.all",
                    'choices' => array(
                        "Oui" => "oui",
                        "Non" => "non"
                    ),
                    'required' => false,
                ))
                ->add('classe_virtuelle', CheckboxType::class, array(
                    'label'    => 'admin.participation.search.classe_virtuelle',
                    'value' => "true",
                    'required' => false,
                ))
                ->add('e_learning', CheckboxType::class, array(
                    'label'    => 'admin.participation.search.e_learning',
                    'value' => "true",
                    'required' => false,
                ))
                ->add('sur_site', CheckboxType::class, array(
                    'label'    => 'admin.participation.search.sur_site',
                    'value' => "true",
                    'required' => false,
                ))
                ;
        }

        if($options['displayCoordinatorFilter']) {
            $builder->add('coordinator', EntityType::class, array(
                'class'         => Person::class,
                'label'         => 'admin.participant.coordinator',
                'choice_label'  => function ($person) {
                    return $person->getLastname() . ' ' . $person->getFirstname();
                },
                'expanded'      => false,
                'multiple'      => false,
                'required'      => false,
                'placeholder'   => 'admin.global.all',
                'query_builder' => function (EntityRepository $er) use ($options) {
                    $qb = $er->createQueryBuilder('p')
                        ->where('p.roles LIKE :roles')
                        ->andWhere("p.isArchived = false")
                        ->setParameter('roles', '%"ROLE_COORDINATOR"%')
                        ->orderBy('p.lastname', 'ASC');

                    if ($options["supervisorId"]) {
                        $qb->andWhere("p.supervisor = :supervisor")->setParameter("supervisor", $options["supervisorId"]);
                    }

                    return $qb;
                },
            ));
        }
        if($options['displayWebmasterFilter']) {
            $builder->add('webmaster', EntityType::class, array(
                'class' => Person::class,
                'label'    => 'admin.user.webmaster.title',
                'choice_label' => function($person) {
                    return $person->getLastname().' '.$person->getFirstname();
                },
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'placeholder' => 'admin.global.all',
                'query_builder' => function (EntityRepository $er) use ($options) {
                    $qb = $er->createQueryBuilder('p')
                        ->where('p.roles LIKE :roles')
                        ->innerJoin("p.coordinatorsPersonWebmaster", "c")
                        ->setParameter('roles', '%"ROLE_WEBMASTER"%')
                        ->orderBy('p.lastname', 'ASC')
                    ;

                    return $qb;
                },
            ));
        }
    }

    public function configureOptions(OptionsResolver $resolver): void {
        $resolver->setDefaults(array(
            'data_class' => AttestationComptabiliteSearch::class,
            'displayCoordinatorFilter' => false,
            'displayWebmasterFilter' => false,
            'displayStartDate' => false,
            'supervisorId' => null,
        ));
    }

    public function getBlockPrefix(): string {
        return 'eduprat_comptabilite_search';
    }

}
