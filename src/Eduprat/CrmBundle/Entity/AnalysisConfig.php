<?php

namespace Eduprat\CrmBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;


/**
 * AnalysisConfig
 */
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: 'Eduprat\CrmBundle\Repository\AnalysisConfigRepository')]
#[ORM\Table(name: 'crm_analysis_config')]
class AnalysisConfig
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'category', type: Types::STRING, length: 255)]
    private ?string $category = null;

    /**
     * @var int
     */
    #[ORM\Column(name: 'year', type: Types::INTEGER)]
    private ?int $year = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'ca', type: Types::DECIMAL, scale: 2)]
    private ?string $ca = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'hours', type: Types::INTEGER)]
    private ?int $hours = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'createdAt', type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'updatedAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    /**
     * Audit constructor.
     */
    public function __construct($category, $year)
    {
        $this->createdAt = new \DateTime();
        $this->category = $category;
        $this->year = $year;
        $this->ca = 0;
        $this->hours = 0;
    }

    /**
     * #ORM\PreUpdate
     */
    public function setPreUpdate()
    {
        $this->updatedAt = new \DateTime();
    }

    /**
     * Get id
     *
     * @return integer 
     */
    public function getId()
    {
        return $this->id;
    }

    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function setCategory(string $category): self
    {
        $this->category = $category;

        return $this;
    }

    public function getYear(): ?int
    {
        return $this->year;
    }

    public function setYear(int $year): self
    {
        $this->year = $year;

        return $this;
    }

    public function getCa(): ?string
    {
        return $this->ca;
    }

    public function setCa(string $ca): self
    {
        $this->ca = $ca;

        return $this;
    }

    public function getHours(): ?int
    {
        return $this->hours;
    }

    public function setHours(int $hours): self
    {
        $this->hours = $hours;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTimeInterface $updatedAt): self
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }


}
