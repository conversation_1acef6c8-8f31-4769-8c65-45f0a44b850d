<?php

namespace Eduprat\ApiBundle\Services;

use Eduprat\DomainBundle\Entity\Programme;
use Symfony\Component\HttpFoundation\Request;

class PresenceManager
{
    public function getPresenceInfo(Request $request, $presence): ?array
    {
        $picturesBaseUrl = $request->getScheme() . '://' . $request->getHttpHost() . $request->getBasePath() . "/img/precenses/";

        switch ($presence) {
            case programme::PRESENCE_SITE:
                return array(
                    "name"  => $presence,
                    "color" => "#009498",
                    "logo"  => $picturesBaseUrl . "sur_site.png"
                );
            case programme::PRESENCE_VIRTUELLE:
                return array(
                    "name"  => $presence,
                    "color" => "#ED6F2E",
                    "logo"  => $picturesBaseUrl . "classe_virtuelle.png"
                );
            case programme::PRESENCE_ELEARNING:
                return array(
                    "name"  => $presence,
                    "color" => "#005572",
                    "logo"  => $picturesBaseUrl . "e_learning.png"
                );
        }
        return null;
    }
}