<?php

namespace Eduprat\ApiBundle\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\ApiBundle\Exception\ApiException;
use Eduprat\ApiBundle\Services\ResponseFactory;
use Eduprat\ApiBundle\Services\Serializer;
use Ed<PERSON>rat\DomainBundle\Services\EmailSender;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Validator\Validator\ValidatorInterface;

abstract class ApiController extends AbstractController
{
    const DEFAULT_GROUP = 'api';

    /**
     * @var ResponseFactory
     */
    protected $responseFactory;
    
    /**
     * @var Serializer
     */
    protected $serializer;

    /**
     * @var ValidatorInterface
     */
    protected $validator;

    /**
     * @var EntityManagerInterface
     */
    protected $entityManager;

    /**
     * @var EmailSender
     */
    protected $emailSender;

    /** @var string */
    private $group;

    /**
     * ApiController constructor.
     * @param $elasticSerializer
     * @param ResponseFactory $responseFactory
     */
    public function __construct(Serializer $serializer, ResponseFactory $responseFactory, ValidatorInterface $validator, EntityManagerInterface $entityManager, RequestStack $requestStack, EmailSender $emailSender)
    {
        $this->responseFactory = $responseFactory;
        $this->serializer = $serializer;
        $this->validator = $validator;
        $this->entityManager = $entityManager;
        $this->emailSender = $emailSender;
        $this->group = Serializer::getGroupFromRequest($requestStack->getCurrentRequest());
    }

    /**
     * @param $entity
     * @param string $group
     * @param array $options
     * @param string $format
     * @return JsonResponse
     */
    public function returnSerializedEntity($entity, $group = self::DEFAULT_GROUP, array $options = [], $format = "json") {
        $groups = array($group, $this->group);
        return $this->responseFactory->buildSuccessFromJson($this->serializer->serializeGroups($entity, $groups, $options, $format));
    }

    /**
     * @param Request $request
     * @param string $class
     * @param array|null $context
     * @return object
     * @throws ApiException
     */
    public function getBody(Request $request, $class = null, array $context = array())
    {
        $json = json_decode($request->getContent(), true);

        if (is_null($json) && !empty($request->getContent())) {
            throw new ApiException(array(array("message" => "La chaîne de caractères JSON est invalide")));
        }

        if (is_null($class)) {
            return $json;
        }

        if ($class && !empty($request->getContent())) {
            $allowedAttributes = array_map(function(\ReflectionProperty $p) {
                return $p->name;
            }, (new \ReflectionClass($class))->getProperties());

            foreach (array_keys($json) as $bodyAttribute) {
                if (!in_array($bodyAttribute, $allowedAttributes)) {
                    throw new ApiException(array(
                        array(
                            "property_path" => $bodyAttribute,
                            "invalid_value" => array($bodyAttribute => $json[$bodyAttribute]),
                            "message" => "Cet attribut n'est pas autorisé"
                        )
                    ));
                }
            }
        }

        /** @var $class $data */
        $data = $this->serializer->deserialize($request->getContent(), $class, "json", $context);
        $violations = $this->validator->validate($data, null, isset($context["groups"]) ? $context["groups"] : null);
        if (count($violations) > 0) {
            throw new ApiException($violations);
        }
        return $data;
    }

    public function handleJsonForm(Request $request, $class, $entity, $options) {
        $form = $this->submitJsonForm($request, $class, $entity, $options);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($entity);
            $this->entityManager->flush();
            return $this->responseFactory->buildSuccess();
        } else {
            $this->validateForm($form);
        }

        return $this->responseFactory->buildError();
    }

    /**
     * @param Request $request
     * @param $class
     * @param $entity
     * @param $options
     * @return FormInterface
     * @throws ApiException
     */
    public function submitJsonForm(Request $request, $class, $entity, $options) {
        $data = $this->getBody($request);
        $form = $this->createForm($class, $entity, $options);
        $form->submit($data);
        return $form;
    }

    public function validateForm(FormInterface $form) {
        $violations = $this->validator->validate($form);
        if (count($violations) > 0) {
            throw new ApiException($violations);
        }
    }

}
