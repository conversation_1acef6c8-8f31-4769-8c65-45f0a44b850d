<?php

namespace Eduprat\ApiBundle\Controller\ApiSite;

use Eduprat\ApiBundle\Controller\ApiController;
use Eduprat\DomainBundle\Form\ProgrammeType;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

#[Route(path: '/professions')]
class ProfessionController extends ApiController
{
    /**
     * @return JsonResponse
     */
    #[Route(path: '', name: 'api_site_professions')]
    public function profession(): JsonResponse
    {
//        $res = ProgrammeType::getGroupes();
        $res = ProgrammeType::groupProfessionAndSpecialities();
        return $this->json($res);
    }
}