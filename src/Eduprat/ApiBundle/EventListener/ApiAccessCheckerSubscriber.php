<?php

namespace Eduprat\ApiBundle\EventListener;

use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;

class ApiAccessCheckerSubscriber implements EventSubscriberInterface
{
    private ParameterBagInterface $parameterBag;

    public function __construct(ParameterBagInterface $parameterBag)
    {
        $this->parameterBag = $parameterBag;
    }

    public function checkApiAccess(RequestEvent $event)
    {
        if (!$event->isMainRequest()) {
            // don't do anything if it's not the master request
            return;
        }
        $expectedStart = '/apiSite/V2/';
        if (substr($event->getRequest()->getRequestUri(),0, strlen($expectedStart)) == $expectedStart) {
            if (!$event->getRequest()->headers->has('api-key')) {
                $event->setResponse(new JsonResponse(['status' => "KO"], Response::HTTP_UNAUTHORIZED));
            } elseif ($event->getRequest()->headers->get('api-key') != $this->parameterBag->get('api_key')) {
                $event->setResponse(new JsonResponse(['status' => "KO"], Response::HTTP_FORBIDDEN));
            }
        }
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => [
                ['checkApiAccess', 0],
            ],
        ];
    }
}