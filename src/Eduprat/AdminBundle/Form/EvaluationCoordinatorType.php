<?php

namespace Eduprat\AdminBundle\Form;

use Ed<PERSON><PERSON>\DomainBundle\Entity\EvaluationQuestion;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class EvaluationCoordinatorType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        /**
         * @var EvaluationQuestion $question
         */
        foreach ($options['questions'] as $index => $question) {
            if($question->getIndex() == 5) {                
                $builder->add($question->getIndex(), TextareaType::class, array(
                    'label' => $question->getLabel(),
                ));
            }
            else {
                $builder->add($question->getIndex(), ChoiceType::class, array(
                    'label' => $question->getLabel(),
                    "expanded" => true,
                    "multiple" => false,
                    "choices" => ['OUI' => 1, 'NON' => 2],
                    'label_attr' => array('class' => 'radio-inline')
                ));
            }
        }
    }

    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            "questions" => []
        ));
    }

}
