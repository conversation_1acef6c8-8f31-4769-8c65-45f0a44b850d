<?php

namespace Eduprat\AdminBundle\Form;

use Ed<PERSON>rat\DomainBundle\Entity\Programme;
use Eduprat\AdminBundle\Services\ProgrammeBuilder;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;

class SelectFormationCreateType extends AbstractType
{

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('formationType', ChoiceType::class, array(
                'label' => 'admin.programme.typeLabel',
                'expanded' => true,
                'choices' => [
                    'admin.programme.types.VFC' => ProgrammeBuilder::VFC_FORMATION_TYPE,
                    'admin.programme.types.TCS' => ProgrammeBuilder::TCS_FORMATION_TYPE,
                    'admin.programme.types.other' => ProgrammeBuilder::OTHER_FORMATION_TYPE,
                ],
            ))
            ->add('presence', ChoiceType::class, array(
                'label' => 'admin.programme.presenceLabel',
                'required' => true,
                'expanded' => true,
                'choices' => [
                    'admin.programme.presences.surSite' => Programme::PRESENCE_SITE,
                    'admin.programme.presences.elearning' => Programme::PRESENCE_ELEARNING,
                    'admin.programme.presences.classeVirtuelle' => Programme::PRESENCE_VIRTUELLE,
                ],
            ))
            ->add('submit', SubmitType::class, array(
                'label' => 'admin.global.selectionner',
                'attr' => [
                    'class' => 'btn btn-eduprat',
                ]
            ))
        ;
    }
}
