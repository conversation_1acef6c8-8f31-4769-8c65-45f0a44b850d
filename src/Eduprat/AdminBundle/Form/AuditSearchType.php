<?php

namespace Eduprat\AdminBundle\Form;

use Alienor\FormBundle\Form\AbstractBaseType;

use Doctrine\ORM\EntityRepository;
use Eduprat\DomainBundle\Entity\Audit;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Eduprat\AdminBundle\Entity\AuditSearch;

class AuditSearchType extends AbstractBaseType {

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('label', null, array(
                'label'    => 'admin.audit.label',
                'required' => false,
            ))
            ->add('category', EntityType::class, array(
                'class' => 'Eduprat\DomainBundle\Entity\AuditCategory',
                'label'    => 'admin.audit.category.label',
                'choice_label' => 'name',
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'placeholder' => 'admin.global.select',
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('c')
                        ->orderBy('c.name', 'ASC');
                },
            ))
            ->add('archived', CheckboxType::class, array(
                'label'    => 'admin.audit.archived',
                'value' => "true",
                'required' => false,
                'attr' => array('class' => 'hidden')
            ))
        ;

        if ($options["type"] === "audit") {
            $builder->add('type', ChoiceType::class, array(
                'required' => true,
                'label'    => 'admin.audit.type',
                'choices' => array(
                    Audit::TYPE_LABEL_AUDIT => Audit::TYPE_AUDIT,
                    Audit::TYPE_LABEL_PREDEFINED => Audit::TYPE_PREDEFINED,
                    Audit::TYPE_LABEL_VIGNETTE => Audit::TYPE_VIGNETTE,
                    "Vignettes cliniques pré" => "Vignettes cliniques Pré",
                    "Vignettes cliniques post" => "Vignettes cliniques Post",
                )
            ));
        }

        $builder->add('year', ChoiceType::class, array(
            'label' => 'admin.programme.year',
            'required' => false,
            'choices'  => self::getYears(),
            'placeholder' => 'admin.global.select',
        ));
    }

    public function configureOptions(OptionsResolver $resolver): void {
        $resolver->setDefaults(array(
            'data_class' => AuditSearch::class,
            "type" => "audit"
        ));
    }

    public function getBlockPrefix(): string {
        return 'eduprat_audit_search';
    }

    public function getYears() {
        $yearArray = [];
        $year = 2017;
        while ($year <= date("Y")+1) {
            $yearArray[$year] = $year;
            $year++;
        }
        return $yearArray;
    }

}
