<?php

namespace Eduprat\AdminBundle\Form;

use Alienor\FormBundle\Form\AbstractBaseType;

use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class EvaluationUserSearchType extends AbstractBaseType {

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('firstname', TextType::class, array(
                'required' => false,
                'label' => 'login.firstname'
            ))
            ->add('lastname', TextType::class, array(
                'required' => false,
                'label' => 'login.lastname'
            ))
            ->add('role', ChoiceType::class, array(
                'choices' => array(
                    'user.role.ROLE_COORDINATOR' => 'ROLE_COORDINATOR',
                    'user.role.ROLE_FORMER' => 'ROLE_FORMER',
                ),
                'required' => false,
                'placeholder' => 'admin.global.select',
                'label' => "login.roles"
            ))
            ->add('start', DateTimeType::class, array(
                'format' => 'dd/MM/yyyy',
                'widget' => 'single_text',
                'html5' => false,
                'attr' => ['provider' => 'datepicker', 'class' => 'datepicker form-control'],
                'label' => 'admin.formation.search.start',
                'required' => false,
            ))
            ->add('end', DateTimeType::class, array(
                'format' => 'dd/MM/yyyy',
                'widget' => 'single_text',
                'html5' => false,
                'attr' => ['provider' => 'datepicker', 'class' => 'datepicker form-control'],
                'label' => 'admin.formation.search.end',
                'required' => false,
            ))
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void {
        $resolver->setDefaults(array(
            'data_class' => 'Eduprat\AdminBundle\Model\EvaluationUserSearch',
        ));
    }

    public function getBlockPrefix(): string {
        return 'eduprat_person_search';
    }

}
