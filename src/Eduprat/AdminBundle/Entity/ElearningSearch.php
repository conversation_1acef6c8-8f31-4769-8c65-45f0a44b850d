<?php

namespace Eduprat\AdminBundle\Entity;
use Symfony\Component\HttpFoundation\Request;

/**
 * Entité pour moteur de recherche
 */
class ElearningSearch
{

    /**
     * @var string
     */
    private $category;

    /**
     * @var string
     */
    private $label;

    /**
     * @var string
     */
    private $year;

    /**
     * @var bool
     */
    private $archived = false;

    /**
     * @return string
     */
    public function getCategory()
    {
        return $this->category;
    }

    /**
     * @param string $category
     * @return ElearningSearch
     */
    public function setCategory($category)
    {
        $this->category = $category;

        return $this;
    }

    /**
     * @return string
     */
    public function getLabel()
    {
        return $this->label;
    }

    /**
     * @param string $label
     * @return ElearningSearch
     */
    public function setLabel($label)
    {
        $this->label = $label;
        return $this;
    }

     /**
     * @return string
     */
    public function getYear()
    {
        return $this->year;
    }

    /**
     * @param string $year
     * @return ElearningSearch
     */
    public function setYear($year)
    {
        $this->year = $year;
        return $this;
    }

    /**
     * @return bool
     */
    public function getArchived()
    {
        return json_decode($this->archived);
    }

    /**
     * @param bool $archived
     */
    public function setArchived($archived)
    {
        $this->archived = $archived;
    }

    public function toArray() {
        return array(
            'category' => $this->getCategory(),
            'label' => $this->getLabel(),
            'archived' => $this->getArchived() ? "true" : "false",
            'year' => $this->getYear(),
        );
    }

    public function getParams() {
        $params = [];
        foreach ($this->toArray() as $name => $value) {
            if ($value) {
                $params[$name] = $value;
            }
        }
        return $params;
    }

    public function isValid() {
        return !empty($this->getParams());
    }

    public function handleRequest(Request $request) {
        foreach ($this->toArray() as $name => $value) {
            if ($request->query->has($name)) {
                $this->$name = $request->query->get($name);
            }
        }
    }

    public function transform() {
        if ($this->getCategory()) {
            $this->setCategory($this->getCategory()->getId());
        }
    }

}