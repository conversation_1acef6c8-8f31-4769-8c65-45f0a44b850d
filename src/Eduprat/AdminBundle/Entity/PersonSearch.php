<?php

namespace Eduprat\AdminBundle\Entity;
use Symfony\Component\HttpFoundation\Request;


/**
 * Entité pour moteur de recherche
 */
class PersonSearch
{
    /**
     * @var string
     */
    private $firstname;

    /**
     * @var string
     */
    private $lastname;

    /**
     * @var string
     */
    private $role;

    /**
     * @var Person
     */
    private $supervisor;

    /**
     * @var string
     */
    private $status;

    /**
     * @var string[]
     */
    private $ugas;

    /**
     * @var string
     */
    private $linkedWebmaster;

    /**
     * @var int
     */
    private $year;

    /**
     * @return string
     */
    public function getFirstname()
    {
        return $this->firstname;
    }

    /**
     * @param string $firstname
     * @return PersonSearch
     */
    public function setFirstname($firstname)
    {
        $this->firstname = $firstname;

        return $this;
    }

    /**
     * @return string
     */
    public function getLastname()
    {
        return $this->lastname;
    }

    /**
     * @param string $lastname
     * @return PersonSearch
     */
    public function setLastname($lastname)
    {
        $this->lastname = $lastname;

        return $this;
    }

    /**
     * @return string
     */
    public function getRole()
    {
        return $this->role;
    }

    /**
     * @return Person
     */
    public function getSupervisor()
    {
        return $this->supervisor;
    }

    /**
     * @param Person $supervisor
     * @return PersonSearch
     */
    public function setSupervisor($supervisor)
    {
        $this->supervisor = $supervisor;
        return $this;
    }

    /**
     * @param string $role
     * @return PersonSearch
     */
    public function setRole($role)
    {
        $this->role = $role;

        return $this;
    }

    /**
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param string $status
     * @return PersonSearch
     */
    public function setStatus($status)
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return array
     */
    public function getUgas()
    {
        return $this->ugas;
    }

    /**
     * @param string $ugas
     * @return PersonSearch
     */
    public function setUgas($ugas)
    {
        $this->ugas = $ugas;
        return $this;
    }

    /**
     * @return string
     */
    public function getLinkedWebmaster()
    {
        return $this->linkedWebmaster;
    }

    /**
     * @param string $linkedWebmaster
     * @return PersonSearch
     */
    public function setLinkedWebmaster($linkedWebmaster)
    {
        $this->linkedWebmaster = $linkedWebmaster;
        return $this;
    }

    /**
     * @return int
     */
    public function getYear()
    {
        return $this->year;
    }

    /**
     * @param int $year
     * @return PersonSearch
     */
    public function setYear($year)
    {
        $this->year = $year;

        return $this;
    }

    public function toArray() {
        return array(
            'firstname' => $this->getFirstname(),
            'lastname' => $this->getLastname(),
            'role' => $this->getRole(),
            'status' => $this->getStatus(),
            'linkedWebmaster' => $this->getLinkedWebmaster(),
            'ugas' => $this->getUgas(),
        );
    }

    public function getParams() {
        $params = [];
        foreach ($this->toArray() as $name => $value) {
            if ($value) {
                $params[$name] = $value;
            }
        }
        return $params;
    }

    public function isValid() {
        return !empty($this->getParams());
    }

    public function handleRequest(Request $request) {
        foreach ($this->toArray() as $name => $value) {
            if ($request->query->has($name)) {
                if (in_array($name, array('ugas'))) {
                    $this->$name = $request->query->all($name);
                }
                else {
                    $this->$name = $request->query->get($name);
                }
            }
        }
    }

}