<?php

namespace Eduprat\AdminBundle\Entity;

use Eduprat\AdminBundle\Form\ParticipantSearchType;
use Eduprat\AdminBundle\Model\AbstractSearch;
use Eduprat\DomainBundle\Entity\FinanceMode;
use Eduprat\DomainBundle\Entity\Participant;

/**
 * Entité pour moteur de recherche
 */
class ParticipantSearch extends AbstractSearch
{
    const SEARCH_FORM = ParticipantSearchType::class;

    public ?FinanceMode $financeMode = null;
    public ?string $id = null;
    public ?string $lastname = null;
    public ?string $firstname = null;
    public ?string $adeli = null;
    public ?string $rpps = null;
    public ?string $zipCode = null;
    public ?string $isCreated = null;
    public ?string $isProspect = null;
    public ?string $region = null;
    public ?array $regionZipCodes = null;
    public ?string $city = null;
    public ?string $speciality = null;
    public ?Participant $uga = null;
    public ?string $ugaName = null;
    public ?string $category = null;
    public ?Person $coordinator = null;
    public ?int $coordinatorId = null;
    public ?array $coordinatorUgas = null;
    public ?int $formerId = null;
    public ?int $supervisorId = null;
    public ?string $gdprAgreement = null;
    public ?string $gdprAgreementPost = null;
    public ?string $gdprAgreementCall = null;
    public ?string $exerciseMode = null;
    public ?string $presence = null;
    public ?string $email = null;
    public ?string $programmeTitle = null;
    public ?array $formationClass = null;
    public ?string $reference = null;
    public ?string $nbSession = null;
    public ?string $type = null;
    public ?array $departement = null;
    public ?string $withReference = null;
    public ?string $withNbSession = null;
    public ?string $withType = null;
    public ?string $withDepartement = null;
    public ?string $withTitre = null;
    public ?string $formType = null;
    public ?array $status = null;
    public ?string $partenariat = null;
    public ?string $isActif = null;

    public function setUserFilters(Person $person)
    {
        if ($person->isCoordinator()) {
            $this->coordinatorId = $person->getId();
            $this->coordinatorUgas = $person->getUgas();
        } else if ($person->isPharmacieFormer()) {
            $this->formerId = $person->getId();
        } else if ($person->isSupervisor()) {
            $this->supervisorId = $person->getId();
            $this->coordinatorUgas = $person->getSupervisorUgas();
        }
        
        if ($this->coordinator instanceof Person) {
            $this->coordinatorId = $this->coordinator->getId();
            $this->coordinatorUgas = $this->coordinator->getUgas();
        }
    }

    public function toArray()
    {
        $array = parent::toArray();
        $array['coordinatorUgas'] = null;
        return $array;
    }
}