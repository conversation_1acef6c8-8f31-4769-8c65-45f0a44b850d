<?php

namespace Eduprat\AdminBundle\Entity;
use Eduprat\DomainBundle\Entity\FinanceMode;
use Symfony\Component\HttpFoundation\Request;


/**
 * Entité pour moteur de recherche
 */
class FinanceSousModeSearch
{
    /**
     * @var string
     */
    private $keyword;

    /**
     * @var string
     */
    private $zip;

    /**
     * @var string
     */
    private $city;

    /**
     * @var FinanceMode
     */
    private $financeMode;

    /**
     * @var string
     *
     */
    private $priseEnCharge;

    /**
     * @var bool
     */
    private $actalians;

    /**
     * @return string
     */
    public function getKeyword()
    {
        return $this->keyword;
    }

    /**
     * @param string $keyword
     * @return FinanceSousModeSearch
     */
    public function setKeyword($keyword)
    {
        $this->keyword = $keyword;
        return $this;
    }

    /**
     * @return string
     */
    public function getZip()
    {
        return $this->zip;
    }

    /**
     * @param string $zip
     * @return FinanceSousModeSearch
     */
    public function setZip($zip)
    {
        $this->zip = $zip;
        return $this;
    }

    /**
     * @return string
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * @param string $city
     * @return FinanceSousModeSearch
     */
    public function setCity($city)
    {
        $this->city = $city;
        return $this;
    }

    /**
     * @return FinanceMode
     */
    public function getFinanceMode()
    {
        return $this->financeMode;
    }

    /**
     * @param FinanceMode $financeMode
     * @return FinanceSousModeSearch
     */
    public function setFinanceMode($financeMode)
    {
        $this->financeMode = $financeMode;
        return $this;
    }

    /**
     * @return string
     */
    public function getPriseEnCharge()
    {
        return $this->priseEnCharge;
    }

    /**
     * @param string $priseEnCharge
     * @return FinanceSousMode
     */
    public function setPriseEnCharge($priseEnCharge)
    {
        $this->priseEnCharge = $priseEnCharge;
        return $this;
    }

    /**
     * @return bool
     */
    public function isActalians()
    {
        return (boolean) $this->actalians;
    }

    /**
     * @param bool $actalians
     * @return FinanceSousModeSearch
     */
    public function setActalians($actalians)
    {
        $this->actalians = $actalians;
        return $this;
    }

    public function toArray() {
        return array(
            'keyword' => $this->getKeyword(),
            'zip' => $this->getZip(),
            'city' => $this->getCity(),
            'financeMode' => $this->getFinanceMode(),
            'priseEnCharge' => $this->getPriseEnCharge(),
            // 'actalians' => $this->isActalians(),
        );
    }

    public function getParams() {
        $params = [];
        foreach ($this->toArray() as $name => $value) {
            if ($value) {
                $params[$name] = $value;
            }
        }
        return $params;
    }

    public function isValid() {
        return !empty($this->getParams());
    }

    public function handleRequest(Request $request) {
        foreach ($this->toArray() as $name => $value) {
            if ($request->query->has($name)) {
                $this->$name = $request->query->get($name);
            }
        }
    }

    public function transform() {
        if ($this->getCity()) {
            $this->setCity($this->getCity()->getId());
        }

        if ($this->getFinanceMode()) {
            $this->setFinanceMode($this->getFinanceMode()->getId());
        }
    }

}