<?php

namespace Eduprat\AdminBundle\Services;

class Regions {

    protected $regionFr2016 = array("84" => "Auvergne-Rhône-Alpes",
                                    "27" => "Bourgogne-France-Comté",
                                    "53" => "Bretagne",
                                    "24" => "Centre-Val de Loire",
                                    "94" => "Corse",
                                    "44" => "Grand Est",
                                    "32" => "Haut-de-France",
                                    "11" => "Ile-de-France",
                                    "28" => "Normandie",
                                    "75" => "Nouvelle-Aquitaine",
                                    "76" => "Occitanie",
                                    "52" => "Pays-de-la-Loire",
                                    "93" => "Provence-Alpes-Côte-d'Azur",
                                    "1" => "Guadeloupe",
                                    "2" => "Martinique",
                                    "3" => "Guyane",
                                    "4" => "La Réunion",
                                    "6" => "Mayotte");

    protected $depFr = array("01" => array("regionId"=>"84", "nom"=>"Ain", "article"=>"L'", "cheflieu"=>"Bourg-en-Bresse", "habitant"=>""),
    "02" => array("regionId"=>"32", "nom"=>"Aisne", "article"=>"L'", "cheflieu"=>"Laon", "habitant"=>"Axonais"),
    "03" => array("regionId"=>"84", "nom"=>"Allier", "article"=>"L'", "cheflieu"=>"Moulins", "habitant"=>"Bourbonnais, Élavérin"),
    "04" => array("regionId"=>"93", "nom"=>"Alpes-de-Haute-Provence", "article"=>"Les", "cheflieu"=>"Digne-les-Bains", "habitant"=>"Bas-Alpin"),
    "06" => array("regionId"=>"93", "nom"=>"Alpes-Maritimes", "article"=>"Les", "cheflieu"=>"Nice", "habitant"=>"Maralpin"),
    "07" => array("regionId"=>"84", "nom"=>"Ardèche", "article"=>"L'", "cheflieu"=>"Privas", "habitant"=>"Ardéchois"),
    "08" => array("regionId"=>"44", "nom"=>"Ardennes", "article"=>"Les", "cheflieu"=>"Charleville-Mézières", "habitant"=>"Ardennais"),
    "09" => array("regionId"=>"76", "nom"=>"Ariège", "article"=>"L'", "cheflieu"=>"Foix", "habitant"=>"Ariégeois"),
    "10" => array("regionId"=>"44", "nom"=>"Aube", "article"=>"L'", "cheflieu"=>"Troyes", "habitant"=>"Aubois"),
    "11" => array("regionId"=>"76", "nom"=>"Aude", "article"=>"L'", "cheflieu"=>"Carcassonne", "habitant"=>"Audois"),
    "12" => array("regionId"=>"76", "nom"=>"Aveyron", "article"=>"L'", "cheflieu"=>"Rodez", "habitant"=>"Aveyronnais"),
    "67" => array("regionId"=>"44", "nom"=>"Bas-Rhin", "article"=>"Le", "cheflieu"=>"Strasbourg", "habitant"=>"Bas-Rhinois"),
    "13" => array("regionId"=>"93", "nom"=>"Bouches-du-Rhône", "article"=>"Les", "cheflieu"=>"Marseille", "habitant"=>"Bucco-Rhodanien"),
    "14" => array("regionId"=>"28", "nom"=>"Calvados", "article"=>"Le", "cheflieu"=>"Caen", "habitant"=>"Calvadosien"),
    "15" => array("regionId"=>"84", "nom"=>"Cantal", "article"=>"Le", "cheflieu"=>"Aurillac", "habitant"=>"Cantalien"),
    "16" => array("regionId"=>"75", "nom"=>"Charente", "article"=>"La", "cheflieu"=>"Angoulême", "habitant"=>"Charentais"),
    "17" => array("regionId"=>"75", "nom"=>"Charente-Maritime", "article"=>"La", "cheflieu"=>"La Rochelle", "habitant"=>"Charentais-Maritime"),
    "18" => array("regionId"=>"24", "nom"=>"Cher", "article"=>"Le", "cheflieu"=>"Bourges", "habitant"=>""),
    "19" => array("regionId"=>"75", "nom"=>"Corrèze", "article"=>"La", "cheflieu"=>"Tulle", "habitant"=>"Corrézien"),
    "2A" => array("regionId"=>"94", "nom"=>"Corse-du-Sud", "article"=>"La", "cheflieu"=>"Ajaccio", "habitant"=>"Corse"),
    "21" => array("regionId"=>"27", "nom"=>"Côte-d'Or", "article"=>"La", "cheflieu"=>"Dijon", "habitant"=>"Côte-d’Orien, Costalorien"),
    "22" => array("regionId"=>"53", "nom"=>"Côtes-d'Armor", "article"=>"Les", "cheflieu"=>"Saint-Brieuc", "habitant"=>"Costarmoricain"),
    "23" => array("regionId"=>"75", "nom"=>"Creuse", "article"=>"La", "cheflieu"=>"Guéret", "habitant"=>"Creusois"),
    "79" => array("regionId"=>"75", "nom"=>"Deux-Sèvres", "article"=>"Les", "cheflieu"=>"Niort", "habitant"=>"Deux-Sévrien"),
    "24" => array("regionId"=>"75", "nom"=>"Dordogne", "article"=>"La", "cheflieu"=>"Périgueux", "habitant"=>"Dordognais"),
    "25" => array("regionId"=>"27", "nom"=>"Doubs", "article"=>"Le", "cheflieu"=>"Besançon", "habitant"=>"Doubien"),
    "26" => array("regionId"=>"84", "nom"=>"Drôme", "article"=>"La", "cheflieu"=>"Valence", "habitant"=>"Drômois"),
    "91" => array("regionId"=>"11", "nom"=>"Essonne", "article"=>"L'", "cheflieu"=>"Évry", "habitant"=>"Essonnien"),
    "27" => array("regionId"=>"28", "nom"=>"Eure", "article"=>"L'", "cheflieu"=>"Évreux", "habitant"=>"Eurois"),
    "28" => array("regionId"=>"24", "nom"=>"Eure-et-Loir", "article"=>"L'", "cheflieu"=>"Chartres", "habitant"=>"Eurélien"),
    "29" => array("regionId"=>"53", "nom"=>"Finistère", "article"=>"Le", "cheflieu"=>"Quimper", "habitant"=>"Finistérien"),
    "30" => array("regionId"=>"76", "nom"=>"Gard", "article"=>"Le", "cheflieu"=>"Nîmes", "habitant"=>"Gardois"),
    "32" => array("regionId"=>"76", "nom"=>"Gers", "article"=>"Le", "cheflieu"=>"Auch", "habitant"=>"Gersois"),
    "33" => array("regionId"=>"75", "nom"=>"Gironde", "article"=>"La", "cheflieu"=>"Bordeaux", "habitant"=>"Girondin"),
    "971" => array("regionId"=>"1", "nom"=>"Guadeloupe", "article"=>"La", "cheflieu"=>"Basse-Terre", "habitant"=>"Guadeloupéen"),
    "973" => array("regionId"=>"3", "nom"=>"Guyane", "article"=>"La", "cheflieu"=>"Cayenne", "habitant"=>"Guyanais"),
    "05" => array("regionId"=>"93", "nom"=>"Hautes-Alpes", "article"=>"Les", "cheflieu"=>"Gap", "habitant"=>"Haut-Alpin"),
    "65" => array("regionId"=>"76", "nom"=>"Hautes-Pyrénées", "article"=>"Les", "cheflieu"=>"Tarbes", "habitant"=>"Haut-Pyrénéen"),
    "2B" => array("regionId"=>"94", "nom"=>"Haute-Corse", "article"=>"La", "cheflieu"=>"Bastia", "habitant"=>"Corse"),
    "31" => array("regionId"=>"76", "nom"=>"Haute-Garonne", "article"=>"La", "cheflieu"=>"Toulouse", "habitant"=>"Haut-Garonnais"),
    "43" => array("regionId"=>"84", "nom"=>"Haute-Loire", "article"=>"La", "cheflieu"=>"Le Puy-en-Velay", "habitant"=>"Altiligérien"),
    "52" => array("regionId"=>"44", "nom"=>"Haute-Marne", "article"=>"La", "cheflieu"=>"Chaumont", "habitant"=>"Haut-Marnais"),
    "70" => array("regionId"=>"27", "nom"=>"Haute-Saône", "article"=>"La", "cheflieu"=>"Vesoul", "habitant"=>"Haut-Saônois"),
    "74" => array("regionId"=>"84", "nom"=>"Haute-Savoie", "article"=>"La", "cheflieu"=>"Annecy", "habitant"=>"Haut-Savoyard"),
    "87" => array("regionId"=>"75", "nom"=>"Haute-Vienne", "article"=>"La", "cheflieu"=>"Limoges", "habitant"=>"Haut-Viennois"),
    "92" => array("regionId"=>"11", "nom"=>"Hauts-de-Seine", "article"=>"Les", "cheflieu"=>"Nanterre", "habitant"=>"Altoséquanais"),
    "68" => array("regionId"=>"44", "nom"=>"Haut-Rhin", "article"=>"Le", "cheflieu"=>"Colmar", "habitant"=>"Haut-Rhinois"),
    "34" => array("regionId"=>"76", "nom"=>"Hérault", "article"=>"L'", "cheflieu"=>"Montpellier", "habitant"=>"Héraultais"),
    "35" => array("regionId"=>"53", "nom"=>"Ille-et-Vilaine", "article"=>"L'", "cheflieu"=>"Rennes", "habitant"=>"Brétillien"),
    "36" => array("regionId"=>"24", "nom"=>"Indre", "article"=>"L'", "cheflieu"=>"Châteauroux", "habitant"=>"Indrien"),
    "37" => array("regionId"=>"24", "nom"=>"Indre-et-Loire", "article"=>"L'", "cheflieu"=>"Tours", "habitant"=>"Tourangeau"),
    "38" => array("regionId"=>"84", "nom"=>"Isère", "article"=>"L'", "cheflieu"=>"Grenoble", "habitant"=>"Isérois, Iseran"),
    "39" => array("regionId"=>"27", "nom"=>"Jura", "article"=>"Le", "cheflieu"=>"Lons-le-Saunier", "habitant"=>"Jurassien"),
    "40" => array("regionId"=>"75", "nom"=>"Landes", "article"=>"Les", "cheflieu"=>"Mont-de-Marsan", "habitant"=>"Landais"),
    "974" => array("regionId"=>"4", "nom"=>"La Réunion", "article"=>"La", "cheflieu"=>"Saint-Denis", "habitant"=>"Réunionais"),
    "42" => array("regionId"=>"84", "nom"=>"Loire", "article"=>"La", "cheflieu"=>"Saint-Étienne", "habitant"=>"Ligérien"),
    "45" => array("regionId"=>"24", "nom"=>"Loiret", "article"=>"Le", "cheflieu"=>"Orléans", "habitant"=>"Loirétain"),
    "44" => array("regionId"=>"52", "nom"=>"Loire-Atlantique", "article"=>"La", "cheflieu"=>"Nantes", "habitant"=>"Mariligérien"),
    "41" => array("regionId"=>"24", "nom"=>"Loir-et-Cher", "article"=>"Le", "cheflieu"=>"Blois", "habitant"=>"Loir-et-Chérien"),
    "46" => array("regionId"=>"76", "nom"=>"Lot", "article"=>"Le", "cheflieu"=>"Cahors", "habitant"=>"Lotois"),
    "47" => array("regionId"=>"75", "nom"=>"Lot-et-Garonne", "article"=>"Le", "cheflieu"=>"Agen", "habitant"=>"Lot-et-Garonnais"),
    "48" => array("regionId"=>"76", "nom"=>"Lozère", "article"=>"La", "cheflieu"=>"Mende", "habitant"=>"Lozèrien"),
    "49" => array("regionId"=>"52", "nom"=>"Maine-et-Loire", "article"=>"Le", "cheflieu"=>"Angers", "habitant"=>"Mainoligérien"),
    "50" => array("regionId"=>"28", "nom"=>"Manche", "article"=>"La", "cheflieu"=>"Saint-Lô", "habitant"=>"Manchois"),
    "51" => array("regionId"=>"44", "nom"=>"Marne", "article"=>"La", "cheflieu"=>"Châlons-en-Champagne", "habitant"=>"Marnais"),
    "972" => array("regionId"=>"2", "nom"=>"Martinique", "article"=>"La", "cheflieu"=>"Fort-de-France", "habitant"=>"Martiniquais"),
    "53" => array("regionId"=>"52", "nom"=>"Mayenne", "article"=>"La", "cheflieu"=>"Laval", "habitant"=>"Mayennais"),
    "976" => array("regionId"=>"6", "nom"=>"Mayotte", "article"=>"", "cheflieu"=>"Dzaoudzi", "habitant"=>"Mahorais"),
    "54" => array("regionId"=>"44", "nom"=>"Meurthe-et-Moselle", "article"=>"La", "cheflieu"=>"Nancy", "habitant"=>"Meurthe-et-Mosellan"),
    "55" => array("regionId"=>"44", "nom"=>"Meuse", "article"=>"La", "cheflieu"=>"Bar-le-Duc", "habitant"=>"Meusien"),
    "56" => array("regionId"=>"53", "nom"=>"Morbihan", "article"=>"Le", "cheflieu"=>"Vannes", "habitant"=>"Morbihannais"),
    "57" => array("regionId"=>"44", "nom"=>"Moselle", "article"=>"La", "cheflieu"=>"Metz", "habitant"=>"Mosellan"),
    "58" => array("regionId"=>"27", "nom"=>"Nièvre", "article"=>"La", "cheflieu"=>"Nevers", "habitant"=>"Nivernais"),
    "59" => array("regionId"=>"32", "nom"=>"Nord", "article"=>"Le", "cheflieu"=>"Lille", "habitant"=>"Nordiste"),
    "60" => array("regionId"=>"32", "nom"=>"Oise", "article"=>"L'", "cheflieu"=>"Beauvais", "habitant"=>"Isarien"),
    "61" => array("regionId"=>"28", "nom"=>"Orne", "article"=>"L'", "cheflieu"=>"Alençon", "habitant"=>"Ornais"),
    "75" => array("regionId"=>"11", "nom"=>"Paris", "article"=>"", "cheflieu"=>"Paris", "habitant"=>"Parisien"),
    "62" => array("regionId"=>"32", "nom"=>"Pas-de-Calais", "article"=>"Le", "cheflieu"=>"Arras", "habitant"=>"Pas-de-Calaisien"),
    "63" => array("regionId"=>"84", "nom"=>"Puy-de-Dôme", "article"=>"Le", "cheflieu"=>"Clermont-Ferrand", "habitant"=>"Puydomois"),
    "64" => array("regionId"=>"75", "nom"=>"Pyrénées-Atlantiques", "article"=>"Les", "cheflieu"=>"Pau", "habitant"=>"Béarnais"),
    "66" => array("regionId"=>"76", "nom"=>"Pyrénées-Orientales", "article"=>"Les", "cheflieu"=>"Perpignan", "habitant"=>"Pyrénaliens"),
    "69" => array("regionId"=>"84", "nom"=>"Rhône", "article"=>"Le", "cheflieu"=>"Lyon", "habitant"=>"Rhodanien"),
    "71" => array("regionId"=>"27", "nom"=>"Saône-et-Loire", "article"=>"La", "cheflieu"=>"Mâcon", "habitant"=>"Saône-et-Loirien"),
    "72" => array("regionId"=>"52", "nom"=>"Sarthe", "article"=>"La", "cheflieu"=>"Le Mans", "habitant"=>"Sarthois"),
    "73" => array("regionId"=>"84", "nom"=>"Savoie", "article"=>"La", "cheflieu"=>"Chambéry", "habitant"=>"Savoyard"),
    "77" => array("regionId"=>"11", "nom"=>"Seine-et-Marne", "article"=>"La", "cheflieu"=>"Melun", "habitant"=>"Seine-et-Marnais"),
    "76" => array("regionId"=>"28", "nom"=>"Seine-Maritime", "article"=>"La", "cheflieu"=>"Rouen", "habitant"=>"Seinomarin"),
    "93" => array("regionId"=>"11", "nom"=>"Seine-Saint-Denis", "article"=>"La", "cheflieu"=>"Bobigny", "habitant"=>"Séquano-Dyonisien"),
    "80" => array("regionId"=>"32", "nom"=>"Somme", "article"=>"La", "cheflieu"=>"Amiens", "habitant"=>"Samarien"),
    "81" => array("regionId"=>"76", "nom"=>"Tarn", "article"=>"Le", "cheflieu"=>"Albi", "habitant"=>"Tarnais"),
    "82" => array("regionId"=>"76", "nom"=>"Tarn-et-Garonne", "article"=>"Le", "cheflieu"=>"Montauban", "habitant"=>"Tarn-et-Garonnais"),
    "90" => array("regionId"=>"27", "nom"=>"Territoire de Belfort", "article"=>"Le", "cheflieu"=>"Belfort", "habitant"=>"Belfortain"),
    "94" => array("regionId"=>"11", "nom"=>"Val-de-Marne", "article"=>"Le", "cheflieu"=>"Créteil", "habitant"=>"Valdemarnais"),
    "95" => array("regionId"=>"11", "nom"=>"Val-d'Oise", "article"=>"Le", "cheflieu"=>"Pontoise", "habitant"=>"Valdoisien"),
    "83" => array("regionId"=>"93", "nom"=>"Var", "article"=>"Le", "cheflieu"=>"Toulon", "habitant"=>"Varois"),
    "84" => array("regionId"=>"93", "nom"=>"Vaucluse", "article"=>"Le", "cheflieu"=>"Avignon", "habitant"=>"Vauclusien"),
    "85" => array("regionId"=>"52", "nom"=>"Vendée", "article"=>"La", "cheflieu"=>"La Roche-sur-Yon", "habitant"=>"Vendéen"),
    "86" => array("regionId"=>"75", "nom"=>"Vienne", "article"=>"La", "cheflieu"=>"Poitiers", "habitant"=>"Viennois"),
    "88" => array("regionId"=>"44", "nom"=>"Vosges", "article"=>"Les", "cheflieu"=>"Épinal", "habitant"=>"Vosgien"),
    "89" => array("regionId"=>"27", "nom"=>"Yonne", "article"=>"L'", "cheflieu"=>"Auxerre", "habitant"=>"Icaunais"),
    "78" => array("regionId"=>"11", "nom"=>"Yvelines", "article"=>"Les", "cheflieu"=>"Versailles", "habitant"=>"Yvelinois"));

    public function getRegions($zipCodes) {
        $regions = array();

        foreach ($zipCodes as $key => $value) {
            $dep = substr($value, 0, 2);
            
            if($dep == '97') {
                $dep = substr($value, 0, 3);
            }

            if(array_key_exists ($dep , $this->depFr)) {
                if(!isset($regions[$this->regionFr2016[$this->depFr[$dep]['regionId']]])) {
                    $regions[$this->regionFr2016[$this->depFr[$dep]['regionId']]] = $this->depFr[$dep]['regionId'];
                }
            }
        }

        ksort($regions, SORT_NATURAL | SORT_FLAG_CASE);

        return $regions;
    }

    public function getDepsByRegion($regionId) {
        $deps = array();
        foreach($this->depFr as $key => $value) {
            if($regionId == $value['regionId']) {
                if(!in_array($key, $deps)) {
                    $deps[] = $key;
                }
            }
        }
        return $deps;
    }

    public function getAllRegions() {
        return array_flip($this->regionFr2016);
    }
}