<?php

namespace Eduprat\AdminBundle\Services;

use Eduprat\DomainBundle\Builder\ModuleBuilder;
use Eduprat\DomainBundle\Builder\StepBuilder;
use Eduprat\DomainBundle\Entity\FormationTcs;
use Eduprat\DomainBundle\Entity\Course;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\ModuleTimes;
use Eduprat\DomainBundle\Entity\ModuleMinTimes;

class FormationTcsBuilder extends FormationBuilder
{
    public function buildCourse(): Course
    {
        $step1 = StepBuilder::aStep()->withModules(
            ModuleBuilder::aStep1Video()->mandatory()->atPosition(1),
            ModuleBuilder::aFormPre()->mandatory()->atPosition(2),
            ModuleBuilder::aPreRestitution()->mandatory()->atPosition(3),
            ModuleBuilder::aStep1Etutorat()->mandatory()->isEndModule()->atPosition(4)
        )->atPosition(1)->build();

        $step2 = StepBuilder::aStep()->withModules(
            ModuleBuilder::aReunion()->atPosition(1)
        )->atPosition(2)->build();

        $step3 = StepBuilder::aStep()->withModules(
            ModuleBuilder::aFicheAction()->atPosition(1),
            ModuleBuilder::aSynthese()->atPosition(2),
            ModuleBuilder::aStep4Etutorat()->atPosition(3),
            ModuleBuilder::aSatisfaction()->atPosition(4),
            ModuleBuilder::aTopos()->atPosition(5),
            ModuleBuilder::aToolBox()->atPosition(6),
            ModuleBuilder::aDocPedagogique()->atPosition(7),
            ModuleBuilder::aEndModule()->atPosition(8)
        )->atPosition(3)->build();

        return new Course([$step1, $step2, $step3]);
    }

    public function buildFormation($formationClass, $programme): FormationTcs
    {
        $formation = new $formationClass($this->buildCourse());
        $formation->setProgramme($programme);
        $formation->addModuleTime(new ModuleTimes(Formation::TYPE_TCS));
        $formation->addModuleMinTime(new ModuleMinTimes($programme));
        return $formation;
    }
}
