<?php

namespace Eduprat\AdminBundle\Services;

use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\ModuleTimes;
use Eduprat\DomainBundle\Entity\ModuleMinTimes;

class FormationBuilder implements FormationBuilderInterface
{
    public function buildCourse() {
        return null;
    }
    
    public function buildFormation($formationClass, $programme): Formation
    {
        $formation = new $formationClass();
        $formation->setProgramme($programme);
        $formation->addModuleTime(new ModuleTimes());
        $formation->addModuleMinTime(new ModuleMinTimes($programme));
        return $formation;
    }
}
