<?php

namespace Eduprat\AdminBundle\Services;

use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Coordinator;
use Doctrine\Common\Collections\ArrayCollection;

class CoordinatorHonorary {

    /**
     * @var int
     */
    protected $tva;

    /**
     * @var \DateTime
     */
    protected $migrationDate;

    public function __construct($tva, $migrationDate) {
        $this->tva = $tva;
        $this->migrationDate = new \DateTime($migrationDate);
    }

    public function isNewHonorary(Formation $formation) {
        return $formation->getStartDate() >= $this->migrationDate;
    }

    public function calculHonorarySession(Formation $formation, Coordinator $coordinator, $n1 = false) 
    {
        $coordinators = $formation->getCoordinators();
        $nbCoordinator = $coordinators->count();

        $participations = $formation->getParticipations();
        $iterator = $participations->getIterator();
    
        $participations = new ArrayCollection(iterator_to_array($iterator));
    
        $compensations = array('total' => 0);

        $formateurs = $formation->getFormateurs();
        $formateursSupplement = 0;

        foreach($formateurs as $formateur) {
            if($formateur->getHonorary() > 400) {
                $formateursSupplement += ($formateur->getHonorary() - 400) / $nbCoordinator;
            }
        }

        $compensations['formateursSupplement'] = $formateursSupplement;

        foreach ($participations as $participation) {
            $useIt = true;
            if($nbCoordinator > 1) {
                if($participation->getCoordinator() == null || $participation->getCoordinator()->getId() != $coordinator->getId()) {
                    $useIt = false;
                }
            }

            if($useIt) {
                $category = $participation->getParticipant()->getCategory();
                $price = $n1 ? $participation->getPriceYearN1() : $participation->getPrice();
                $budgetCR = $participation->getBudgetCR();
        
                if(!isset($compensations[$category][$price][$budgetCR]['nbParticipant'])) {
                    $compensations[$category][$price][$budgetCR]['nbParticipant'] = 0;
                }
                $compensations[$category][$price][$budgetCR]['nbParticipant']++;
        
                if(!isset($compensations[$category][$price][$budgetCR])) {
                    $compensations[$category][$price][$budgetCR] = array();
                }
        
                if(!isset($compensations[$category][$price][$budgetCR]['nbHour'])) {
                    $compensations[$category][$price][$budgetCR]['nbHour'] = $participation->getNbHour();
                }
        
                if(!isset($compensations[$category][$price][$budgetCR]['totalBudgetCR'])) {
                    $compensations[$category][$price][$budgetCR]['totalBudgetCR'] = 0;
                }
                $compensations[$category][$price][$budgetCR]['totalBudgetCR'] +=  $budgetCR;
        
                $compensations['total'] += $budgetCR;
            }
        }

        $compensations['totalTtc'] = $compensations['total'] + $compensations['total'] * $this->tva / 100;
    
        return $compensations;
    }

    public function calculTotalHonorary(Formation $formation, Coordinator $coordinator, $substractRestauration = false, $checkIfExist = false, $substractFormateursSupplement = false, $n1 = false)
    {
        $budgetCR = array('total' => 0, 'newHonorary' => $this->isNewHonorary($formation));

        if ($budgetCR['newHonorary']) {
            $budgetCR['total'] = $coordinator->getCalculatedHonorary($n1);
            return $budgetCR;
        }

        $restaurationHonorary = $coordinator->getRestaurationHonorary();

        $nbCoordinator = $formation->getCoordinators()->count();

        $honorary = $coordinator->getHonorary();

        if($checkIfExist && $honorary) {
            $budgetCR['total'] = $honorary;
            return $budgetCR;
        }

        $formateursSupplement = 0;

        $ref = $formation->getProgramme()->getReference();
        $budgetCR[$ref]['title'] = $formation->getProgramme()->getTitle();

        $formateurs = $formation->getFormateurs();

        foreach($formateurs as $formateur) {
            if($formateur->getHonorary() > 400) {
                $formateursSupplement += ($formateur->getHonorary() - 400) / $nbCoordinator;
            }
        }

        if(!isset($budgetCR[$ref])) {
            $budgetCR[$ref] = array();
        }

        $participations = $formation->getParticipations();
        $iterator = $participations->getIterator();
        $participations = new ArrayCollection(iterator_to_array($iterator));

        $budgetCR[$ref]['total'] = 0;
        foreach ($participations as $participation) {
            $useIt = true;
            if($nbCoordinator > 1) {
                if($participation->getCoordinator() == null || $participation->getCoordinator()->getId() != $coordinator->getId()) {
                    $useIt = false;
                }
            }
            if ($useIt) {
                $budget = $participation->getBudgetCR();
                $budgetCR[$ref]['total'] += $budget;
                $budgetCR['total'] += $budget;
            }
        }

        $budgetCR['formateursSupplement'] = $formateursSupplement;

        if($substractFormateursSupplement) {
            $budgetCR['total'] -= $formateursSupplement;
        }

        if($substractRestauration) {
            $budgetCR['total'] -= $restaurationHonorary;
        }

        $budgetCR['totalTtc'] = $budgetCR['total'] + $budgetCR['total'] * $this->tva / 100;
    
        return $budgetCR;
    }

    public function displayCoordinatorsHonoraryByFormation(Formation $formation)
    {
        $display = false;
        $programme = $formation->getProgramme();

        if($formation->getCoordinators()) {
            $coordinators = $formation->getCoordinators();

            $display = array();

            foreach ($coordinators as $key => $coordinator) {
                $compensation = $this->calculHonorarySession($formation, $coordinator);
                // if($compensation['total'] > 0) {
                    $display[$coordinator->getId()] = true;
                // }
            }
        }
        
        return $display;
    }

    public function displayCoordinatorsHonoraryByProgramme(Formation $formation)
    {
        $display = false;

        if($formation->getCoordinators()) {
            $coordinators = $formation->getCoordinators();

            $display = array();

            foreach ($coordinators as $key => $coordinator) {
                $display[$coordinator->getId()] = true;
            }
        }
        
        return $display;
    }
}