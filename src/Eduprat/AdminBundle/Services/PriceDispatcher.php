<?php

namespace Eduprat\AdminBundle\Services;

class PriceDispatcher
{
    // Stockage de la valeur de priceN1 dans ce tableau
    private const REPARTITIONS = [
        "2025-2026" => [
            "Médecin" => 370.50,
            "Masseur-kinésithérapeute" =>222.30,
            "Pédicure-podologue" => 156,
            "Infirmier" => 222.30,
            "Sage-Femme" => 257.40,
            "Pharmacien" => 214.50,
            "Biologiste" => 327.60,
            "Orthophoniste" => 84,
            "Orthoptiste" => 163.80,
            "Chirurgien-dentiste" => 257.40,
        ]
    ];

    // Répartition des indemnisations sur price et priceYearN1 de la participation
    // pour les sessions pluriannuelles et pour les participations qui matchent avec self::REPARTITIONS
    public function dispatchPluriannualPrice($participation): void
    {
        $formation = $participation->getFormation();
        $participationPrice = floatval($participation->getPrice());
        if ($formation->isPluriannuelle() && $participation->getFinanceSousMode()->getName() === "GIP Agence Nationale du DPC") {
            $pluriYears = $formation->getOpeningDate()->format("Y") . "-" . $formation->getClosingDate()->format("Y");
            if (
                isset(self::REPARTITIONS[$pluriYears]) &&
                isset(self::REPARTITIONS[$pluriYears][$participation->getParticipant()->getCategory()]) 
                ) {
                    // Si le PriceN1 stocké dans le tableau est <= au coût global de la participation, on répartit
                    if (self::REPARTITIONS[$pluriYears][$participation->getParticipant()->getCategory()] <= $participationPrice) {
                        $price = $participationPrice - self::REPARTITIONS[$pluriYears][$participation->getParticipant()->getCategory()];
                        $priceN1 = self::REPARTITIONS[$pluriYears][$participation->getParticipant()->getCategory()];
                        $participation->setPrice($price);
                        $participation->setPriceYearN1($priceN1);
                    }
            }
        }
    }
}
