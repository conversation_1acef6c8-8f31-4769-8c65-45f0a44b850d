<?php

namespace Eduprat\AdminBundle\Services;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\AuditBundle\Form\EvaluationType;
use Ed<PERSON>rat\DomainBundle\Entity\BaseQuestion;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\EvaluationQuestion;
use Symfony\Component\Form\FormFactory;
use Eduprat\DomainBundle\Entity\Programme;
use Symfony\Component\Form\FormFactoryInterface;

abstract class EvaluationManagerCoordinator extends FormManagerCoordinator
{
    const NB_QUESTION = 0;

    /**
     * @var ArrayCollection
     */
    private $answers;

    /**
     * @var Programme|Programme
     */
    protected $programme;

    /**
     * @var string
     */
    protected $type = "";

    /**
     * EvaluationManagerCoordinator constructor.
     * @param EntityManager $entityManager
     * @param FormFactory   $formFactory
     * @param Programme $programme
     * @param Coordinator $coordinator
     * @throws \Exception
     */
    public function __construct(EntityManagerInterface $entityManager, FormFactoryInterface $formFactory, Programme $programme, Coordinator $coordinator)
    {
        parent::__construct($entityManager, $formFactory, $programme, $coordinator);
        $this->answers = $this->getAnswers();
    }

    /**
     * @return ArrayCollection
     */
    public function getAnswers(){
        return new ArrayCollection($this->entityManager->getRepository($this->getAnswerClass())->findByProgramme($this->programme));
    }

    /**
     * @inheritdoc
     */
    public function getQuestionIndexCoordinator(Programme $programme, $key, array $options)
    {
        return $key + 1;
    }

    public function getQuestions()
    {
        $questions = new ArrayCollection();
        for ($i = 1; $i <= $this->getNbQuestion(); $i++) {
            $questions->add(new EvaluationQuestion($this->type, $i));
        }
        return $questions;
    }

    public function getNbQuestion()
    {
        return self::NB_QUESTION;
    }

    /**
     * Retourne true si toutes les questions ont été répondues
     * @return bool
     */
    public function isCompleted()
    {
        return $this->answers->count() === $this->questions->count();
    }

    /**
     * @return string
     * @throws \Exception
     */
    public function getAnswerClass()
    {
        throw new \Exception("getAnswerConstructor must be overrided");
    }

    /**
     * Retourne la réponse associée à une question
     * Si elle n'existe pas, une nouvelle réponse est créée
     * @param BaseQuestion $question
     * @return EvaluationFormerAnswer
     */
    public function getAssociatedAnswer(BaseQuestion $question)
    {
        $answer = $this->answers->filter(function($answer) use ($question) {
            /** @var EvaluationFormerAnswer $answer */
            return $answer->getQuestion() === $question->getIndex();
        })->first();

        if (!$answer) {
            $class = $this->getAnswerClass();
            $answer = (new $class())
                ->setCoordinator($this->coordinator)
                ->setProgramme($this->programme)
                ->setQuestion($question->getIndex());
        }

        return $answer;
    }

    /**
     * Transforme les réponses actuelles pour qu'elles soient restaurées dans le formulaire
     * format : clé => valeur
     * avec clé = concaténation de :  id Programme + index Question
     */
    public function getFormAnswers()
    {
        $formAnswers = array();
        $programme = $this->programme;
        $answers = $this->answers;

        $this->questions->forAll(function($key, BaseQuestion $question) use ($programme, $answers, &$formAnswers) {
            $answers->forAll(function ($aKey, $answer) use ($programme, $question, &$formAnswers) {
                if ($question->getIndex() === $answer->getQuestion()) {
                    $formAnswers[$question->getIndex()] = $answer->getAnswer();
                }
                return true;
            });
            return true;
        });
        return $formAnswers;
    }

    public function getForm()
    {
        return $this->formFactory->create(EvaluationType::class, $this->getFormAnswers(), array(
            "questions" => $this->questions
        ));
    }

}