<?php

namespace Eduprat\AdminBundle\Services;

use Doctrine\ORM\ORMException;
use Eduprat\AdminBundle\Entity\PasswordHistory;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Participant;
use Symfony\Component\Security\Core\User\UserInterface;

class UserManager extends \Alienor\UserBundle\Services\UserManager
{
    /**
     * @return Person
     */
    public function createUser(): Person
    {
        return new Person();
    }

    public function createFromParticipant(Participant $participant): Person
    {
        if (!$participant->canCreateAccount()) {
            throw new \LogicException("Ce participant ne peut pas être associé à un compte utilisateur");
        }
        $user = $this->createUser();
        $user->setEmail($participant->getEmail());
        $user->setIsEnabled(true);
        $user->setPlainPassword(substr(md5(uniqid()), 0, 12));
        $this->updatePassword($user);
        return $user;
    }

    /**
     * @param Person $user
     * @throws ORMException
     */
    public function updatePassword(UserInterface $user): void
    {
        parent::updatePassword($user);
        if (!$user->hasRole("ROLE_SUPER_ADMIN")) {
            $this->em->persist($this->createPasswordHistory($user));
        }
    }

    public function createPasswordHistory(UserInterface $person): PasswordHistory
    {
        $history = new PasswordHistory();
        $history->setPerson($person)
            ->setPassword($person->getPassword())
            ->setSalt($person->getSalt());
        return $history;
    }

}