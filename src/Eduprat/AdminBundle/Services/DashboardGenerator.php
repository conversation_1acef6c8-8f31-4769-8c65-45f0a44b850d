<?php

namespace Eduprat\AdminBundle\Services;


use Eduprat\AdminBundle\Entity\Person;

class DashboardGenerator
{

    private $dashboardUrl;

    private $configPath;

    private $config;

    /**
     * DashboardGenerator constructor.
     * @param              $dashboardUrl
     * @param              $configPath
     */
    public function __construct($dashboardUrl, $configPath)
    {
        $this->dashboardUrl = $dashboardUrl;
        $this->configPath = $configPath;
        $this->config = json_decode(file_get_contents($this->configPath), true);
    }

    public function hasDashboard(Person $person) {
        return isset($this->config['users'][$person->getId()]);
    }

    public function getUserConfig(Person $person) {
        if ($person) {
            return $this->config['users'][$person->getId()];
        }
        return [];
    }

    public function getDashboardList(Person $person) {
        if ($person) {
            $list = $this->getUserConfig($person)['dashboards'];
            foreach ($list as &$dashboard) {
                $name = $dashboard;
                $dashboard = $this->config["dashboards"][$dashboard];
                $dashboard["name"] = $name;
            }
            return $list;
        }

        return [];
    }

    public function getFilters(Person $person) {
        if ($person) {
            $list = $this->getUserConfig($person)['filters'];
            foreach ($list as &$filter) {
                if (!is_array($filter)) {
                    $filter = $this->config["filters"][$filter];
                }
            }
            return $list;
        }
        return [];
    }

    public function getDashboard($name) {
        return $this->config["dashboards"][$name];
    }

    public function getDashboardUrl(Person $person, $name) {
        $dashboard = $this->getDashboard($name);
        $filters = array("filters" => $this->getFilters($person));
        $token = $this->encodeToken($dashboard["id"], $dashboard["salt"], $filters);
        return sprintf('%sdashboard/embed/%s?iframe=true&token=%s&%s', $this->dashboardUrl, $dashboard["id"], $token, http_build_query($filters));
    }

    /**
     * @param            $dashboardId
     * @param string     $salt
     * @param array|null $parameters
     * @return string
     */
    public function encodeToken($dashboardId, $salt, $parameters = null) {
        $string = "";
        $string .=  '?CLASS=Alienor\DashboardBundle\Entity\Dashboard';
        $string .=  '?ID='.$dashboardId;
        $string .=  '&SALT='.$salt;
        if (sizeof($parameters) > 0) {
            $string .= json_encode($parameters);
        }
        $string = substr(md5($string), 9, 10) ;
        return $string;
    }

}