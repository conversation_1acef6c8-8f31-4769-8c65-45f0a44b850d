<?php

namespace Eduprat\AdminBundle\Services;

use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\BaseQuestion;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\Programme;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormFactory;
use Symfony\Component\Form\FormFactoryInterface;

abstract class FormManagerCoordinator implements FormManagerInterfaceCoordinator
{
    /**
     * @var EntityManager
     */
    protected $entityManager;

    /**
     * @var FormFactory
     */
    protected $formFactory;

    /**
     * @var Coordinator
     */
    protected $coordinator;

    /**
     * @var Collection
     */
    protected $questions;

    /**
     * @var Programme
     */
    protected $programme;

    /**
     * @var array
     */
    protected $options = array();

    /**
     * FormManagerCoordinator constructor.
     * @param EntityManager $entityManager
     * @param FormFactory   $formFactory
     * @param Programme $programme
     * @param Coordinator $coordinator
     * @throws \Exception
     */
    public function __construct(EntityManagerInterface $entityManager, FormFactoryInterface $formFactory, Programme $programme, Coordinator $coordinator)
    {
        $this->entityManager = $entityManager;
        $this->formFactory = $formFactory;
        $this->programme = $programme;
        $this->coordinator = $coordinator;
        $this->questions = $this->getQuestions();
        $options = $this->options;

        /** On ajoute un index spécifique au formulaire pour chaque question */
        $this->questions->forAll(function($key, $question) use ($programme, $options) {
            /** @var BaseQuestion $question */
            $question->setIndex($this->getQuestionIndexCoordinator($programme, $key, $options));
            return true;
        });
    }

    public function save(Form $form)
    {
        foreach ($this->questions as $index => $question) {
            if (!is_null($formAnswer = $form->get($question->getIndex())->getData())) {
                $answer = $this->getAssociatedAnswer($question);
                $answer->setAnswer($formAnswer);
                $this->entityManager->persist($answer);
            }
        }
        $this->entityManager->flush();
    }

}