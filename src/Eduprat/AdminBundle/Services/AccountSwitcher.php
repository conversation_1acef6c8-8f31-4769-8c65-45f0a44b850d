<?php

namespace Eduprat\AdminBundle\Services;

use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorage;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Http\Event\InteractiveLoginEvent;

class AccountSwitcher
{

    CONST SESSION_KEY = "account_switching";

    /**
     * @var EntityManager
     */
    private $entityManager;

    /**
     * @var SessionInterface
     */
    private $session;

    /**
     * @var EventDispatcherInterface
     */
    private $eventDispatcher;

    /**
     * @var TokenStorage
     */
    private $tokenStorage;
    private RequestStack $requestStack;

    public function __construct(RequestStack $requestStack, EntityManagerInterface $entityManager, EventDispatcherInterface $eventDispatcher, TokenStorageInterface $tokenStorage)
    {
        $this->entityManager = $entityManager;
        $this->eventDispatcher = $eventDispatcher;
        $this->tokenStorage = $tokenStorage;
        $this->requestStack = $requestStack;
    }

    public function prepareAccountSwitching(Person $person): void
    {
        $persons = $this->entityManager->getRepository(Person::class)->findBy(
            array("email" => $person->getEmail()),
            array("roles" => "ASC")
        );

        $accounts = array();
        /** @var Person $person */
        foreach ($persons as $person) {
            if ($person->getParticipant() !== null) {
                $accounts[] = array(
                    "id" => $person->getId(),
                    "role" => Person::ROLE_PARTICIPANT
                );
            }
            if ($person->getRole()) {
                $accounts[] = array(
                    "id" => $person->getId(),
                    "role" => $person->getRole() ?? Person::ROLE_PARTICIPANT
                );
            }
        }

        if ($this->requestStack->getMainRequest()?->hasSession()) {
            $session = $this->requestStack->getSession();
            if ($session) {
                if (count($accounts) > 1) {
                    $session->set(self::SESSION_KEY, $accounts);
                } else {
                    $session->remove(self::SESSION_KEY);
                }
            }
        }

    }
    public function canSwitchAccount(): bool
    {
        if ($this->requestStack->getMainRequest()?->hasSession()) {
            $session = $this->requestStack->getSession();
        }
        return $session?->has(self::SESSION_KEY) ?? false;
    }

    /**
     * @return mixed
     */
    public function getSwitchableAccounts(): mixed
    {
        if ($this->requestStack->getMainRequest()?->hasSession()) {
            $session = $this->requestStack->getSession();
        }
        return $session?->get(self::SESSION_KEY) ?? false;
    }

    /**
     * @param Request $request
     * @param Person $user
     */
    public function switchAccount(Request $request, Person $user): void
    {
        if ($this->requestStack->getMainRequest()?->hasSession()) {
            $session = $this->requestStack->getSession();
            if (!in_array($user->getId(), array_map(function($a) {
                return $a["id"];
            }, $session->get(self::SESSION_KEY)))) {
                throw new AccessDeniedHttpException();
            }

            $token = new UsernamePasswordToken($user, null, "shared_auth", $user->getRoles());
            $this->tokenStorage->setToken($token); //now the user is logged in

            //now dispatch the login event
            $event = new InteractiveLoginEvent($request, $token);
            $this->eventDispatcher->dispatch($event, "security.interactive_login");
        }
    }

}