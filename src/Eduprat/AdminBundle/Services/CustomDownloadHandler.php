<?php

namespace Eduprat\AdminBundle\Services;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpFoundation\StreamedResponse;

use Vich\UploaderBundle\Exception\NoFileFoundException;
use Vich\UploaderBundle\Util\Transliterator;
use Vich\UploaderBundle\Handler\AbstractHandler;

class CustomDownloadHandler extends AbstractHandler
{

    public function downloadObject($object, $field, $disposition = ResponseHeaderBag::DISPOSITION_ATTACHMENT, $className = null, $fileName = null)
    {
        $mapping = $this->getMapping($object, $field, $className);
        $stream  = $this->storage->resolveStream($object, $field, $className);

        if ($stream === null) {
            throw new NoFileFoundException(sprintf('No file found in field "%s".', $field));
        }

        // On force le téléchargement direct si le fichier fait + de 20mo
        if (filesize($this->storage->resolvePath($object, $field)) > 2e+7) {
            $disposition = ResponseHeaderBag::DISPOSITION_ATTACHMENT;
        }

        return $this->createDownloadResponse(
            $stream,
            $fileName ?: $mapping->getFileName($object),
            $disposition
        );
    }

    private function createDownloadResponse($stream, $filename, $disposition = ResponseHeaderBag::DISPOSITION_ATTACHMENT)
    {
        $response = null;
        $mime = 'application/octet-stream';

        if ($disposition === ResponseHeaderBag::DISPOSITION_ATTACHMENT) {
            $response = new StreamedResponse(function () use ($stream) {
                stream_copy_to_stream($stream, fopen('php://output', 'w'));
            });
        } else if ($disposition === ResponseHeaderBag::DISPOSITION_INLINE) {
            $content = stream_get_contents($stream);
            $mime = (new \finfo(FILEINFO_MIME))->buffer($content);
            $response = new Response($content);
        }

        $disposition = $response->headers->makeDisposition(
            $disposition,
            Transliterator::transliterate($filename)
        );
        $response->headers->set('Content-Disposition', $disposition);
        $response->headers->set('Content-Type', $mime);

        return $response;
    }
}
