<?php

namespace Eduprat\AdminBundle\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Form\FormInterface;
use DateTime;
use Doctrine\DBAL\Exception\ForeignKeyConstraintViolationException;
use Ed<PERSON>rat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\Elearning;
use Eduprat\DomainBundle\Entity\Lesson;
use Eduprat\DomainBundle\Form\LessonType;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Audit controller.
 */
#[Route(path: '/lesson')]
#[IsGranted('ROLE_WEBMASTER')]
class LessonController extends EdupratController
{
    #[Route(path: '/{id}/create', name: 'admin_lesson_create')]
    public function create(Request $request, Elearning $elearning, EntityManagerInterface $entityManager)
    {
        $lesson = new Lesson();
        $lesson->setElearning($elearning);
        $lesson->setPosition(count($elearning->getLessons()) + 1);

        $form = $this->createForm(LessonType::class, $lesson);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($lesson);
            $entityManager->flush();

            $this->flashMessages->addSuccess('lesson.create.success');
            return $this->redirectToRoute('admin_elearning_edit', array("id" => $elearning->getId()));
        }

        return $this->render('admin/lesson/create.html.twig', array(
            'formRaw' => $form,
            'lesson' => $lesson,
            )
        );
    }

    #[Route(path: '/{id}/updatePosition', methods: ['POST'], name: 'admin_lesson_updatePosition')]
    public function updatePosition(Request $request, Elearning $elearning, EntityManagerInterface $entityManager): JsonResponse
    {
        $body = json_decode($request->getContent(), true);
        foreach($elearning->getLessons() as $lesson) {
            $lesson->setPosition($body[$lesson->getId()]);
        }
        $entityManager->flush();
        return new JsonResponse(array("status" => "ok"));
    }

    #[Route(path: '/{id}/edit', methods: ['GET', 'POST'], name: 'admin_lesson_edit')]
    public function edit(Request $request, Lesson $lesson, EntityManagerInterface $entityManager)
    {

        $form = $this->createForm(LessonType::class, $lesson);
        $form->handleRequest($request);

        if ($form->isSubmitted() &&  $form->isValid()) {
            // if($cloneYear != $audit->getYear() && count($audit->getFormations())) {
            //     $this->flashMessages->addError('audit.edit.errorChangeDate');
            // } else {
                // foreach ($originalQuestions as $question) {
                //     if (false === $audit->getQuestions()->contains($question)) {
                //         $audit->getQuestions()->removeElement($question);
                //         $entityManager->remove($question);
                //     }
                // }

                $lesson->setUpdatedAt(New DateTime('now'));
                $entityManager->persist($lesson);
                $entityManager->flush();

                $this->flashMessages->addSuccess('lesson.edit.success');
                return $this->redirectToRoute('admin_elearning_edit', array("id" => $lesson->getElearning()->getId()));
            // }
        }

        return $this->render('admin/lesson/create.html.twig', array(
            'lesson' => $lesson,
            'formRaw' => $form
        ));
    }

    #[Route(path: '/{id}/delete', methods: ['GET', 'POST'], name: 'admin_lesson_delete')]
    public function delete(Request $request, Lesson $lesson, EntityManagerInterface $em)
    {
        $form = $this->createDeleteForm($lesson);
        $form->handleRequest($request);
        $elearning = $lesson->getElearning();

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $em->remove($lesson);
                $em->flush();
            } catch (ForeignKeyConstraintViolationException $e) {
                $this->flashMessages->addError('lesson.delete.constraint_error');
                return $this->redirectToRoute('admin_lesson_index');
            }
            $count = 1;
            foreach($elearning->getLessons() as $lesson) {
                $lesson->setPosition($count);
                $count++;
            }
            $em->flush();
            $this->flashMessages->addSuccess('lesson.delete.success');
            return $this->redirectToRoute('admin_elearning_edit', array("id" => $elearning->getId()));
        }

        return $this->render('admin/lesson/delete.html.twig', array(
            'lesson' => $lesson,
            'form' => $form,
        ));
    }

    /**
     * Creates a form to delete a Audit entity.
     */
    private function createDeleteForm(Lesson $lesson): FormInterface
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_lesson_delete', array('id' => $lesson->getId())))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
            ;
    }

    // /**
    //  * Creates a form to delete a Audit entity.
    //  *
    //  * @param Elearning $elearning The Audit entity
    //  *
    //  * @return \Symfony\Component\Form\Form The form
    //  */
    // private function createArchiveForm(Elearning $elearning)
    // {
    //     return $this->createFormBuilder()
    //         ->setAction($this->generateUrl('admin_elearning_archive', array('id' => $elearning->getId())))
    //         ->setMethod('POST')
    //         ->getForm()
    //         ;
    // }

    // /**
    //  * Creates a form to delete a Elearning entity.
    //  *
    //  * @param Elearning $elearning The Elearning entity
    //  *
    //  * @return \Symfony\Component\Form\Form The form
    //  */
    // private function createUnarchiveForm(Elearning $elearning)
    // {
    //     return $this->createFormBuilder()
    //         ->setAction($this->generateUrl('admin_elearning_unarchive', array('id' => $elearning->getId())))
    //         ->setMethod('POST')
    //         ->getForm()
    //         ;
    // }

}
