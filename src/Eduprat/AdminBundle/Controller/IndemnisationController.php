<?php

namespace Eduprat\AdminBundle\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\HttpFoundation\Response;
use Ed<PERSON>rat\DomainBundle\Controller\EdupratController;
use Ed<PERSON>rat\DomainBundle\Entity\Indemnisation;
use Eduprat\DomainBundle\Entity\PriseEnCharge;
use Eduprat\DomainBundle\Form\IndemnisationType;

use Eduprat\DomainBundle\Form\ProgrammeType;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class IndemnisationController
 */
#[Route(path: '/indemnisation-v2')]
#[IsGranted('ROLE_WEBMASTER')]
class IndemnisationController extends EdupratController
{
    #[Route(path: '/{year}', methods: ['GET'], name: 'admin_indemnisation_index', requirements: ['year' => '^(|\d{4})$'])]
    public function index(string $year, EntityManagerInterface $em): Response
    {
        $groups = ProgrammeType::getFlatGroupes();

        $indemnisationsArray = array();
        $formats = array();
        $presences = array();

        foreach ($groups as $value) {
            $indemnisationsArray[$value] = array();
        }

        $indemnisationRepository = $em->getRepository(Indemnisation::class);
        $indemnisations = $indemnisationRepository->findBy(
            array('year' => $year),
            array(
                'group' => 'ASC',
                'format' => 'ASC',
                'presence' => 'ASC',
                'formType' => 'ASC',
                'nbHours' => 'ASC'
            )
        );

        foreach ($indemnisations as $key => $value) {
            $indemnisationsArray[$value->getGroup()][$value->getFormat()][$value->getPresence()][$value->getFormType()][] = $value;
            $formats[$value->getGroup()][$value->getFormat()][] = $value;
            $presences[$value->getGroup()][$value->getFormat()][$value->getPresence()][] = $value;
        }

        $indemnisation = new Indemnisation();
        $form = $this->createForm(IndemnisationType::class, $indemnisation);

        return $this->render('admin/indemnisation/index.html.twig', array(
            'form' => $form,
            'indemnisations' => $indemnisationsArray,
            'formatsCount' => $formats,
            'presencesCount' => $presences,
            'priseEnCharges' => $em->getRepository(PriseEnCharge::class)->findAll(),
            'year' => $year
        ));
    }

    #[Route(path: '/create/{year}', name: 'admin_indemnisation_create', requirements: ['year' => '^(|\d{4})$'])]
    public function create(Request $request, EntityManagerInterface $entityManager, string $year)
    {
        $group = $request->query->get('group');
        $indemnisation = new Indemnisation();
        $indemnisation->setYear($year);

        $form = $this->createForm(IndemnisationType::class, $indemnisation, array(
            'group' => $group
        ));

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

            $indemnisationError = $this->validateIndemnisation($indemnisation, $entityManager, $year);

            if($indemnisationError) {
                $data = [
                    'type' => 'validation_error',
                    'title' => 'There was a validation error',
                    'errors' => [$indemnisationError]
                ];
                return new JsonResponse($data, Response::HTTP_BAD_REQUEST);
            }

            $entityManager->persist($indemnisation);
            $entityManager->flush();

            $this->flashMessages->addSuccess('admin.indemnisation.create.success');
            return $this->redirectToRoute('admin_indemnisation_index', array("year" => $year));
        }

        if ($form->isSubmitted() && !$form->isValid() && $request->isXmlHttpRequest()) {
            $errors = $this->getErrorsFromForm($form);
            $data = [
                'type' => 'validation_error',
                'title' => 'There was a validation error',
                'errors' => $errors
            ];
            return new JsonResponse($data, Response::HTTP_BAD_REQUEST);
        }

        return $this->render('admin/indemnisation/edit.html.twig', array(
            'form' => $form,
        ));
    }

    #[Route(path: '/edit/{id}/{year}', methods: ['GET', 'POST'], name: 'admin_indemnisation_edit', requirements: ['year' => '^(|\d{4})$'])]
    public function edit(Request $request, Indemnisation $indemnisation, EntityManagerInterface $em, string $year)
    {
        $form = $this->createForm(IndemnisationType::class, $indemnisation);
        
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

            $indemnisationError = $this->validateIndemnisation($indemnisation, $em, $year);

            if($indemnisationError) {
                $data = [
                    'type' => 'validation_error',
                    'title' => 'There was a validation error',
                    'errors' => [$indemnisationError]
                ];
                return new JsonResponse($data, Response::HTTP_BAD_REQUEST);
            }

            $em->persist($indemnisation);
            $em->flush();

            $this->flashMessages->addSuccess('admin.indemnisation.edit.success');
            return $this->redirectToRoute('admin_indemnisation_index', array("year" => $year));
        }

        if ($form->isSubmitted() && !$form->isValid() && $request->isXmlHttpRequest()) {
            $errors = $this->getErrorsFromForm($form);
            $data = [
                'type' => 'validation_error',
                'title' => 'There was a validation error',
                'errors' => $errors
            ];
            return new JsonResponse($data, Response::HTTP_BAD_REQUEST);
        }

        return $this->render('admin/indemnisation/edit.html.twig', array(
            'form' => $form,
        ));
    }

    private function getErrorsFromForm(FormInterface $form)
    {
        $errors = array();
        foreach ($form->getErrors() as $error) {
            $errors[] = $error->getMessage();
        }
        foreach ($form->all() as $childForm) {
            if ($childForm instanceof FormInterface) {
                if ($childErrors = $this->getErrorsFromForm($childForm)) {
                    $errors[$childForm->getName()] = $childErrors;
                }
            }
        }
        return $errors;
    }

    #[Route(path: '/{id}/delete/{year}', methods: ['GET', 'POST'], name: 'admin_indemnisation_delete', requirements: ['year' => '^(|\d{4})$'])]
    public function delete(Request $request, Indemnisation $indemnisation, EntityManagerInterface $em, string $year)
    {
        $form = $this->createDeleteForm($indemnisation, $year);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em->remove($indemnisation);
            $em->flush();
            $this->flashMessages->addSuccess('admin.indemnisation.delete.success');
            return $this->redirectToRoute('admin_indemnisation_index', array("year" => $year));
        }

        return $this->render('admin/indemnisation/delete.html.twig', array(
            'indemnisation' => $indemnisation,
            'form' => $form,
            'year' => $year
        ));
    }

    /**
     * Creates a form to delete a Indemnisation entity.
     */
    private function createDeleteForm(Indemnisation $indemnisation, $year): FormInterface
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_indemnisation_delete', array('id' => $indemnisation->getId(), 'year' => $year)))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
            ;
    }

    private function validateIndemnisation(Indemnisation $indemnisation,EntityManagerInterface $em, $year) {
        $priseEnChaseCount = 0;
        foreach($indemnisation->getPriseEnCharges() as $pr) {
            if($pr->getPrice()) {
                $priseEnChaseCount++;
                if($priseEnChaseCount > 1) {
                    return "Vous ne pouvez entrer qu'un montant de prise en charge à la fois";
                    break;
                } 
            }
        }

        $indemnisationRepository = $em->getRepository(Indemnisation::class);
        $indemnisationExist = $indemnisationRepository->findByTriptyqueAndPrice($indemnisation, $year);
        if($indemnisationExist && $indemnisationExist->getId() !== $indemnisation->getId()) {
            return "Cette indemnisation existe déjà";
        }
        
        return false;
    }

    #[Route(path: '/{id}/sendToApi', methods: ['GET', 'POST'], name: 'admin_send_to_api')]
    public function sendToApi(Indemnisation $indemnisation, EntityManagerInterface $entityManager)
    {
        $indemnisation->setSendToApi(!$indemnisation->isSendToApi());
        $entityManager->flush();

        return $indemnisation->isSendToApi() ? $this->json(true) : $this->json(false);
    }
}
