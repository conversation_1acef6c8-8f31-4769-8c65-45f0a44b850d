<?php

namespace Eduprat\AdminBundle\Controller;

use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Doctrine\DBAL\Exception\ForeignKeyConstraintViolationException;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\AdminBundle\Entity\FinanceSousModeSearch;
use Eduprat\AdminBundle\Form\FinanceSousModeSearchType;
use Eduprat\DomainBundle\Entity\FinanceMode;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Form\FinanceSousModeType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class FinanceSousModeController
 */
#[Route(path: '/finance-sous-mode')]
#[IsGranted('ROLE_WEBMASTER')]
class FinanceSousModeController extends EdupratController
{
    /**
     * Lists all FinanceSousMode entities.
     *
     * @return Response
     */
    #[Route(path: '/', methods: ['GET'], name: 'admin_finance_sous_mode_index')]
    public function index(Request $request, EntityManagerInterface $em): Response
    {
        $financeSousModeRepository = $em->getRepository(FinanceSousMode::class);

        $search = new FinanceSousModeSearch();
        $search->handleRequest($request);

        $financeSousModes = $financeSousModeRepository->findSearchResults($search);

        if ($search->getCity()) {
            $search->setCity($financeSousModeRepository->findOneByCity($search->getCity()));
        }

        if ($search->getFinanceMode()) {
            $search->setFinanceMode($em->getRepository(FinanceMode::class)->find($search->getFinanceMode()));
        }

        $searchForm = $this->createForm(FinanceSousModeSearchType::class, $search, array(
            'method' => 'POST',
            'action' => $this->generateUrl('admin_finance_sous_mode_dofilter'),
        ));

        return $this->render('admin/finance_sous_mode/index.html.twig', array(
            'financeSousModes' => $financeSousModes,
            'form' => $searchForm,
            'search' => $search,
            'search_route' => 'admin_finance_sous_mode_index'
        ));
    }

    #[Route(path: '/filter/', name: 'admin_finance_sous_mode_dofilter', methods: ['POST'])]
    public function doFilter(Request $request): RedirectResponse {
        $filter = new FinanceSousModeSearch();
        $form = $this->createForm(FinanceSousModeSearchType::class, $filter);
        $form->handleRequest($request);

        if ($filter->getCity()) {
            $filter->setCity($filter->getCity()->getCity());
        }

        if ($filter->getFinanceMode()) {
            $filter->setFinanceMode($filter->getFinanceMode()->getId());
        }

        if ($form->isSubmitted() && $form->isValid()) {
            return $this->redirectToRoute('admin_finance_sous_mode_index', $filter->getParams());
        }
        return $this->redirectToRoute('admin_finance_sous_mode_index');
    }

    /**
     * @param Request $request
     * @return RedirectResponse|Response
     */
    #[Route(path: '/create', name: 'admin_finance_sous_mode_create')]
    public function create(Request $request, EntityManagerInterface $entityManager) {
        $financeSousMode = new FinanceSousMode();
        $form = $this->createForm(FinanceSousModeType::class, $financeSousMode);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($financeSousMode);
            $entityManager->flush();

            $this->flashMessages->addSuccess('admin.financeSousMode.create.success');
            return $this->redirectToRoute('admin_finance_sous_mode_index');
        }

        return $this->render('admin/finance_sous_mode/create.html.twig', array(
            'form' => $form,
        ));
    }

    /**
     * Displays a form to edit an existing FinanceSousMode entity.
     *
     * @param Request $request
     * @param FinanceSousMode $financeSousMode
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/edit', methods: ['GET', 'POST'], name: 'admin_finance_sous_mode_edit')]
    public function edit(Request $request, FinanceSousMode $financeSousMode, EntityManagerInterface $entityManager) {
        $form = $this->createForm(FinanceSousModeType::class, $financeSousMode);
        $form->handleRequest($request);

        if ($form->isSubmitted() &&  $form->isValid()) {
            $entityManager->persist($financeSousMode);
            $entityManager->flush();

            $this->flashMessages->addSuccess('admin.financeSousMode.edit.success');
            return $this->redirectToRoute('admin_finance_sous_mode_index');
        }

        return $this->render('admin/finance_sous_mode/create.html.twig', array(
            'financeSousMode' => $financeSousMode,
            'form' => $form,
        ));
    }

    /**
     * Deletes a FinanceSousMode entity.
     *
     * @param Request $request
     * @param FinanceSousMode $financeSousMode
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/delete', methods: ['GET', 'POST'], name: 'admin_finance_sous_mode_delete')]
    public function delete(Request $request, FinanceSousMode $financeSousMode, EntityManagerInterface $em)
    {
        $form = $this->createDeleteForm($financeSousMode);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $em->remove($financeSousMode);
                $em->flush();
            } catch (ForeignKeyConstraintViolationException $e) {
                $this->flashMessages->addError('admin.financeSousMode.delete.constraint_error');
                return $this->redirectToRoute('admin_finance_sous_mode_index');
            }

            $this->flashMessages->addSuccess('admin.financeSousMode.delete.success');
            return $this->redirectToRoute('admin_finance_sous_mode_index');
        }

        return $this->render('admin/finance_sous_mode/delete.html.twig', array(
            'financeSousMode' => $financeSousMode,
            'form' => $form,
        ));
    }

    /**
     * Creates a form to delete a FinanceSousMode entity.
     */
    private function createDeleteForm(FinanceSousMode $financeSousMode): FormInterface
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_finance_sous_mode_delete', array('id' => $financeSousMode->getId())))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
            ;
    }
}
