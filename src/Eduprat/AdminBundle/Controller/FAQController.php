<?php

namespace Eduprat\AdminBundle\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Doctrine\DBAL\Exception\ForeignKeyConstraintViolationException;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\FAQ;
use Eduprat\DomainBundle\Form\FAQType;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class FAQController
 */
#[Route(path: '/faq')]
#[IsGranted('ROLE_WEBMASTER')]
class FAQController extends EdupratController
{
    /**
     * Lists all FAQ entities.
     *
     * @return Response
     */
    #[Route(path: '/', methods: ['GET'], name: 'admin_faq_index')]
    public function index(EntityManagerInterface $em): Response
    {
        $faqs = $em->getRepository(FAQ::class)->findBy(array(), array('position' => 'ASC'));
        return $this->render('admin/faq/index.html.twig', array(
            'faqs' => $faqs,
        ));
    }

    /**
     * @param Request $request
     * @return RedirectResponse|Response
     */
    #[Route(path: '/create', name: 'admin_faq_create')]
    public function create(Request $request, EntityManagerInterface $entityManager) {
        $faq = new FAQ();
        $form = $this->createForm(FAQType::class, $faq);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $max = $entityManager->getRepository(FAQ::class)->findMaxPosition();
            $max = empty($max) ? 0 : (int) $max[0]["position"];

            $faq->setPosition($max + 1);
            $entityManager->persist($faq);
            $entityManager->flush();

            $this->flashMessages->addSuccess('admin.faq.create.success');
            return $this->redirectToRoute('admin_faq_index');
        }

        return $this->render('admin/faq/create.html.twig', array(
            'form' => $form,
        ));
    }

    /**
     * Displays a form to edit an existing FAQ entity.
     *
     * @param Request $request
     * @param FAQ $faq
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/edit', methods: ['GET', 'POST'], name: 'admin_faq_edit')]
    public function edit(Request $request, EntityManagerInterface $entityManager, FAQ $faq) {
        $form = $this->createForm(FAQType::class, $faq);
        $form->handleRequest($request);

        if ($form->isSubmitted() &&  $form->isValid()) {
            $entityManager->persist($faq);
            $entityManager->flush();

            $this->flashMessages->addSuccess('admin.faq.edit.success');
            return $this->redirectToRoute('admin_faq_index');
        }

        return $this->render('admin/faq/create.html.twig', array(
            'faq' => $faq,
            'form' => $form,
        ));
    }

    /**
     * Deletes a FAQ entity.
     *
     * @param Request $request
     * @param FAQ $faq
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/delete', methods: ['GET', 'POST'], name: 'admin_faq_delete')]
    public function delete(Request $request, EntityManagerInterface $em, FAQ $faq)
    {
        $form = $this->createDeleteForm($faq);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $em->remove($faq);
                $em->flush();
            } catch (ForeignKeyConstraintViolationException $e) {
                $this->flashMessages->addError('admin.faq.delete.constraint_error');
                return $this->redirectToRoute('admin_faq_index');
            }
            $this->flashMessages->addSuccess('admin.faq.delete.success');
            return $this->redirectToRoute('admin_faq_index');
        }

        return $this->render('admin/faq/delete.html.twig', array(
            'faq' => $faq,
            'form' => $form,
        ));
    }

    /**
     * Creates a form to delete a FAQ entity.
     */
    private function createDeleteForm(FAQ $faq): FormInterface
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_faq_delete', array('id' => $faq->getId())))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
            ;
    }

    /**
     * @param Request $request
     * @return RedirectResponse|Response
     */
    #[Route(path: '/position', methods: ['POST'], name: 'admin_faq_position')]
    public function position(Request $request, EntityManagerInterface $em): JsonResponse {

        $positions = $request->request->all('positions');

        /** @var FAQ[] $faqs */
        $faqs = $em->getRepository(FAQ::class)->findAll();

        foreach ($faqs as $faq) {
            $faq->setPosition(array_search($faq->getId(), $positions));
            $em->persist($faq);
        }

        $em->flush();

        return new JsonResponse(array(
            "status" => "ok",
            "positions" => $positions
        ));
    }

}
