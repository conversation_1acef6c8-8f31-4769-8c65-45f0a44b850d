<?php

namespace Eduprat\AdminBundle\Controller;

use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Doctrine\DBAL\Exception\ForeignKeyConstraintViolationException;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\PriseEnCharge;
use Eduprat\DomainBundle\Form\PriseEnChargeType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormInterface;
use Vich\UploaderBundle\Storage\StorageInterface;

/**
 * Class PriseEnChargeController
 */
#[Route(path: '/prise-en-charge')]
#[IsGranted('ROLE_WEBMASTER')]
class PriseEnChargeController extends EdupratController
{
    /**
     * Lists all PriseEnCharge entities.
     *
     * @return Response
     */
    #[Route(path: '/', methods: ['GET'], name: 'admin_prise_en_charge_index')]
    public function index(EntityManagerInterface $em): Response
    {
        $priseEnCharges = $em->getRepository(PriseEnCharge::class)->findBy(array(), array('name' => 'ASC'));
        return $this->render('admin/prise_en_charge/index.html.twig', array(
            'priseEnCharges' => $priseEnCharges,
        ));
    }

    /**
     * @param Request $request
     * @return RedirectResponse|Response
     */
    #[Route(path: '/create', name: 'admin_prise_en_charge_create')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function create(Request $request, EntityManagerInterface $entityManager, StorageInterface $storage) {

        $priseEnCharge = new PriseEnCharge();

        $form = $this->createForm(PriseEnChargeType::class, $priseEnCharge);
        $form->handleRequest($request);

        $this->checkDuplicatedPicture($form, $priseEnCharge, $request, $storage);

        if ($form->isSubmitted() && $form->isValid()) {


            $entityManager->persist($priseEnCharge);
            $entityManager->flush();

            $this->flashMessages->addSuccess('admin.priseEnCharge.create.success');
            return $this->redirectToRoute('admin_prise_en_charge_index');
        }
        return $this->render('admin/prise_en_charge/create.html.twig', array(
            'form' => $form,
        ));
    }

    /**
     * Displays a form to edit an existing PriseEnCharge entity.
     *
     * @param Request $request
     * @param PriseEnCharge $priseEnCharge
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/edit', methods: ['GET', 'POST'], name: 'admin_prise_en_charge_edit')]
    public function edit(Request $request, EntityManagerInterface $entityManager, PriseEnCharge $priseEnCharge) {
        $form = $this->createForm(PriseEnChargeType::class, $priseEnCharge);
        $form->handleRequest($request);

        if ($form->isSubmitted() &&  $form->isValid()) {
            // l'image ne se mettais pas à jour
            if($form->get('priseEnChargePictureFile')->getData() != null) {
                $priseEnCharge->setPriseEnChargePicture($form->get('priseEnChargePictureFile')->getData());
            }
            $entityManager->persist($priseEnCharge);
            $entityManager->flush();

            $this->flashMessages->addSuccess('admin.priseEnCharge.edit.success');
            return $this->redirectToRoute('admin_prise_en_charge_index');
        }

        return $this->render('admin/prise_en_charge/create.html.twig', array(
            'priseEnCharge' => $priseEnCharge,
            'form' => $form,
        ));
    }

    /**
     * Deletes a PriseEnCharge entity.
     *
     * @param Request $request
     * @param PriseEnCharge $priseEnCharge
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/delete', methods: ['GET', 'POST'], name: 'admin_prise_en_charge_delete')]
    public function delete(Request $request, EntityManagerInterface $em, PriseEnCharge $priseEnCharge)
    {
        $form = $this->createDeleteForm($priseEnCharge);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $em->remove($priseEnCharge);
                $em->flush();
            } catch (ForeignKeyConstraintViolationException $e) {
                $this->flashMessages->addError('admin.priseEnCharge.delete.constraint_error');
                return $this->redirectToRoute('admin_prise_en_charge_index');
            }
            $this->flashMessages->addSuccess('admin.priseEnCharge.delete.success');
            return $this->redirectToRoute('admin_prise_en_charge_index');
        }

        return $this->render('admin/prise_en_charge/delete.html.twig', array(
            'priseEnCharge' => $priseEnCharge,
            'form' => $form,
        ));
    }

    /**
     * Creates a form to delete a PriseEnCharge entity.
     *
     */
    private function createDeleteForm(PriseEnCharge $priseEnCharge): FormInterface
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_prise_en_charge_delete', array('id' => $priseEnCharge->getId())))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
            ;
    }

    /**
     * @param FormInterface $form
     * @param PriseEnCharge $priseEnCharge
     * @param Request $request
     * @param StorageInterface $storage
     */
    public function checkDuplicatedPicture(FormInterface $form, PriseEnCharge $priseEnCharge, Request $request, StorageInterface $storage)
    {
        if ($form->isSubmitted() && $request->query->has("copy")) {
            $priseEnCharge->setPriseEnChargePicture(null);
        }

        if ($form->isSubmitted() && $form->get('priseEnChargePictureFrom')->getData() && is_null($form->get('priseEnChargePictureFile')->getData()) && is_null($priseEnCharge->getPicture())) {
            $priseEnCharge->setPriseEnChargePicture($form->get('priseEnChargePictureFrom')->getData());
            $path = str_replace("//", "/", $filename = $storage->resolvePath($priseEnCharge, "priseEnChargePictureFile"));
            if (!$path || !file_exists($path)) {
                $error = new FormError("L'image sélectionnée n'existe pas");
                $form->get('priseEnChargePictureFile')->addError($error);
            }
        }
    }
}
