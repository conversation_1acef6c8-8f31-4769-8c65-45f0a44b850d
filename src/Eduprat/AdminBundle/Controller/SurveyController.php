<?php

namespace Eduprat\AdminBundle\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\DBAL\Exception\ForeignKeyConstraintViolationException;
use Eduprat\AdminBundle\Entity\AuditSearch;
use Eduprat\AdminBundle\Form\AuditSearchType;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\AuditCategory;
use Eduprat\DomainBundle\Entity\Image;
use Eduprat\DomainBundle\Entity\Survey;
use Eduprat\DomainBundle\Entity\SurveyQuestion;
use Eduprat\DomainBundle\Form\SurveyType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Survey controller.
 */
#[Route(path: '/survey')]
#[IsGranted('ROLE_WEBMASTER')]
class SurveyController extends EdupratController
{
    /**
     * Lists all Survey entities.
     */
    #[Route(path: '/{page}', methods: ['GET'], defaults: ['page' => '1'], name: 'admin_survey_index', requirements: ['page' => '^\d+$'])]
    public function index(string $page, Request $request, EntityManagerInterface $em): Response
    {
        $search = new AuditSearch();
        $search->handleRequest($request);

        if ($search->getCategory()) {
            $search->setCategory($em->getRepository(AuditCategory::class)->find($search->getCategory()));
        }

        $searchForm = $this->createForm(AuditSearchType::class, $search, array(
            'method' => 'POST',
            'action' => $this->generateUrl('admin_survey_dofilter'),
            "type"   => "survey"
        ));

        $searchForm->handleRequest($request);
        $surveyRepository = $em->getRepository(Survey::class);

        $nbPerPage = 30;
        $max = 10;
        $count = $surveyRepository->countSearchResults($search);
        $surveys = $surveyRepository->findSearchResults($search, $page, $nbPerPage);
        $npages = ceil($count / $nbPerPage);
        $current = intval($page);
        $inf = max(1, intval($current - (($max == 1 ? 0 : $max-1) / 2)));
        $sup = min($inf + ($max-1), $npages);
        $pageRange = range($inf, $sup);
        $pagination = array(
            'nb' => $count,
            'page' => $page,
            'npages' => $npages,
            'page_range' => $pageRange,
            'surveys' => $surveys,
        );

        return $this->render('admin/survey/index.html.twig', array_merge($pagination, array(
            'form' => $searchForm->createView(),
            'search' => $search,
        )));
    }

    /**
     * @param Request $request
     * @return RedirectResponse
     */
    #[Route(path: '/filter/', methods: ['POST'], name: 'admin_survey_dofilter')]
    public function doFilter(Request $request): RedirectResponse {
        $filter = new AuditSearch();
        $form = $this->createForm(AuditSearchType::class, $filter);
        $form->handleRequest($request);
        $filter->transform();
        if ($form->isSubmitted() && $form->isValid()) {
            return $this->redirectToRoute('admin_survey_index', $filter->getParams());
        }
        return $this->redirectToRoute('admin_survey_index');
    }

    /**
     * @param Request $request
     * @return RedirectResponse|Response
     */
    #[Route(path: '/create', name: 'admin_survey_create')]
    public function create(Request $request, EntityManagerInterface $entityManager)
    {
        $survey = new Survey();
        $form = $this->createForm(SurveyType::class, $survey);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

            $entityManager->persist($survey);
            $entityManager->flush();

            return $this->redirectToRoute('admin_survey_index');
        }

        return $this->render('admin/survey/create.html.twig', array(
            'form' => $form,
        ));
    }

    /**
     * Displays a form to edit an existing Survey entity.
     */
    #[Route(path: '/{id}/edit', methods: ['GET', 'POST'], name: 'admin_survey_edit')]
    public function edit(Request $request, Survey $survey, EntityManagerInterface $entityManager)
    {
        $form = $this->createForm(SurveyType::class, $survey);

        /** @var SurveyQuestion[] $originalQuestions */
        $originalQuestions = new ArrayCollection();
        $originalChoices = array();

        foreach ($survey->getQuestions() as $question) {
            $originalQuestions->add($question);
            $originalChoices[$question->getId()] = new ArrayCollection();
            foreach ($question->getChoices() as $choice) {
                $originalChoices[$question->getId()]->add($choice);
            }
        }

        $cloneYear = $survey->getYear();

        $form->handleRequest($request);

        if ($form->isSubmitted() &&  $form->isValid()) {

            
            if($cloneYear != $survey->getYear() && count($survey->getFormations())) {
                $this->flashMessages->addError('audit.edit.errorChangeDate');
            } else {

                foreach ($originalQuestions as $question) {
                    if (false === $survey->getQuestions()->contains($question)) {
                        foreach ($originalChoices[$question->getId()] as $choice) {
                            $question->getChoices()->removeElement($choice);
                            $entityManager->remove($choice);
                        }
                        $survey->getQuestions()->removeElement($question);
                        $entityManager->remove($question);
                    } else {
                        foreach ($originalChoices[$question->getId()] as $choice) {
                            if (false === $question->getChoices()->contains($choice)) {
                                $question->getChoices()->removeElement($choice);
                                $entityManager->remove($choice);
                            }
                        }
                    }
                }

                $survey->setUpdatedAt(New DateTime('now'));
                $entityManager->persist($survey);
                $entityManager->flush();

                $this->flashMessages->addSuccess('survey.edit.success');
                return $this->redirectToRoute('admin_survey_index');
            }
        }

        return $this->render('admin/survey/create.html.twig', array(
            'survey' => $survey,
            'form' => $form,
        ));
    }

    /**
     * @param Request $request
     * @param Survey $survey
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/archive', methods: ['GET', 'POST'], name: 'admin_survey_archive')]
    public function archive(Request $request, Survey $survey, EntityManagerInterface $em)
    {
        $form = $this->createArchiveForm($survey);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $survey->setArchived(true);
            $em->persist($survey);
            $em->flush();
            $this->flashMessages->addSuccess('survey.archive.success');
            return $this->redirectToRoute('admin_survey_index', array());
        }

        return $this->render('admin/survey/archive.html.twig', array(
            'survey' => $survey,
            'form' => $form,
        ));
    }

    /**
     * @param Request $request
     * @param Survey $survey
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/unarchive', methods: ['GET', 'POST'], name: 'admin_survey_unarchive')]
    public function unarchive(Request $request, Survey $survey,EntityManagerInterface $em)
    {
        $form = $this->createUnarchiveForm($survey);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $survey->setArchived(false);
            $em->persist($survey);
            $em->flush();
            $this->flashMessages->addSuccess('survey.unarchive.success');
            return $this->redirectToRoute('admin_survey_index', array());
        }

        return $this->render('admin/survey/unarchive.html.twig', array(
            'survey' => $survey,
            'form' => $form,
        ));


    }

    /**
     * @param Request $request
     * @param Survey $survey
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/duplicate', methods: ['GET'], name: 'admin_survey_duplicate')]
    public function duplicate(Survey $survey, EntityManagerInterface $em): RedirectResponse
    {

        $duplicatedSurvey = clone $survey;

        $duplicatedSurvey->setLabel($survey->getLabel() . " - Copie");
        $duplicatedSurvey->setUpdatedAt(new DateTime());

        foreach ($survey->getQuestions() as $question) {
            $duplicatedQuestion = clone $question;
            $duplicatedSurvey->addQuestion($duplicatedQuestion);

            if ($question->getType() === "choice") {
                foreach ($question->getChoices() as $choice) {
                    $duplicatedChoice = clone $choice;
                    $duplicatedQuestion->addChoice($duplicatedChoice);
                }
            }

            foreach ($question->getImages() as $image) {
                $newImage = (new Image())
                    ->setName($image->getName())
                    ->setFilename($image->getFilename())
                    ->setExtension($image->getExtension())
                    ->setSurveyQuestion($duplicatedQuestion)
                    ;
                $duplicatedQuestion->addImage($newImage);
            }

        }

        $em->persist($duplicatedSurvey);
        $em->flush();

        $this->flashMessages->addSuccess('survey.duplicate.success');

        return $this->redirectToRoute('admin_survey_index');
    }

    /**
     * Deletes a Survey entity.
     *
     * @param Request $request
     * @param Survey $survey
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/delete', methods: ['GET', 'POST'], name: 'admin_survey_delete')]
    public function delete(Request $request, Survey $survey, EntityManagerInterface $em)
    {
        $form = $this->createDeleteForm($survey);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $em->remove($survey);
                $em->flush();
            } catch (ForeignKeyConstraintViolationException $e) {
                $this->flashMessages->addError('survey.delete.constraint_error');
                return $this->redirectToRoute('admin_survey_index');
            }
            $this->flashMessages->addSuccess('survey.delete.success');
            return $this->redirectToRoute('admin_survey_index');
        }

        return $this->render('admin/survey/delete.html.twig', array(
            'survey' => $survey,
            'form' => $form,
        ));
    }

    /**
     * Creates a form to delete a Audit entity.
     */
    private function createArchiveForm(Survey $survey): FormInterface
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_survey_archive', array('id' => $survey->getId())))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
            ;
    }

    /**
     * Creates a form to delete a Survey entity.
     */
    private function createUnarchiveForm(Survey $survey): FormInterface
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_survey_unarchive', array('id' => $survey->getId())))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
            ;
    }

    /**
     * Creates a form to delete a Survey entity.
     */
    private function createDeleteForm(Survey $survey): FormInterface
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_survey_delete', array('id' => $survey->getId())))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
            ;
    }

}
