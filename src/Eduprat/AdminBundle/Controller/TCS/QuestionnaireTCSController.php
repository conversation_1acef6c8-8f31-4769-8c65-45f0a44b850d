<?php

namespace Eduprat\AdminBundle\Controller\TCS;

use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\DomainBundle\Controller\EdupratController;
use Ed<PERSON>rat\DomainBundle\DTO\QuestionnaireTCSDto;
use Ed<PERSON>rat\DomainBundle\DTO\QuestionnaireTCSSearch;
use Ed<PERSON>rat\DomainBundle\Entity\AuditCategory;
use Eduprat\DomainBundle\Entity\TCS\QuestionnaireTCS;
use Eduprat\DomainBundle\Form\TCS\QuestionnaireTCSSearchType;
use Eduprat\DomainBundle\Form\TCS\QuestionnaireTCSType;
use Eduprat\DomainBundle\Repository\TCS\ExpertRepository;
use Ed<PERSON>rat\DomainBundle\Repository\TCS\QuestionnaireTCSRepository;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Ed<PERSON>rat\DomainBundle\Services\TcsQuestionScoreCalculator;

#[Route('/tcs/questionnaire')]
class QuestionnaireTCSController extends Ed<PERSON>ratController
{
    #[Route('/search/{page}', name: 'app_admin_tcs_questionnaire_tcs_index', methods: ['GET', 'POST'])]
    public function index(Request $request, QuestionnaireTCSRepository $questionnaireTCSRepository, EntityManagerInterface $em, int $page = 1): Response
    {
        $thematique = null;
        $label = null;
        if ($request->query->has('label')) {
            $label = $request->query->get('label');
        }
        if ($request->query->has('thematique')) {
            $thematique = $request->query->get('thematique');
            $thematique = $thematique ? $em->getRepository(AuditCategory::class)->find($thematique) : null;
        }
        $search = new QuestionnaireTCSSearch($label, $thematique, false);

        $form = $this->createForm(QuestionnaireTCSSearchType::class, $search);
        $form->handleRequest($request);

        $sortBy = $request->query->get('sortBy');
        $order = $request->query->get('order');

        $nbPerPage = 30;
        $max = 10;
        $count = $questionnaireTCSRepository->countSearchResults($search);
        $questionnaire_tcs = $questionnaireTCSRepository->findSearchResults($search, $page, $nbPerPage, $sortBy, $order);
        $npages = ceil($count / $nbPerPage);
        $current = intval($page);
        $inf = max(1, intval($current - (($max == 1 ? 0 : $max-1) / 2)));
        $sup = min($inf + ($max-1), $npages);
        $pageRange = range($inf, $sup);
        $pagination = array(
            'nb' => $count,
            'page' => $page,
            'npages' => $npages,
            'page_range' => $pageRange,
            'questionnaire_tcs' => $questionnaire_tcs,
        );

        return $this->render('admin/tcs/questionnaire_tcs/index.html.twig', array_merge($pagination, array(
            'form' => $form->createView(),
            'search' => $search,
            'extraParams' => array_merge($search->toArray(), array(
                'sortBy' => $sortBy,
                'order' => $order
            ))
        )));
    }

    #[Route('/new', name: 'app_admin_tcs_questionnaire_tcs_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $questionnaireTCDto = new QuestionnaireTCSDto();
        $form = $this->createForm(QuestionnaireTCSType::class, $questionnaireTCDto);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist(QuestionnaireTCS::fromDTO($questionnaireTCDto));
            $entityManager->flush();

            return $this->redirectToRoute('app_admin_tcs_questionnaire_tcs_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/tcs/questionnaire_tcs/new.html.twig', [
            'form' => $form,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_admin_tcs_questionnaire_tcs_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, QuestionnaireTCS $questionnaireTC, EntityManagerInterface $entityManager): Response
    {
        $questionnaireTCDto = new QuestionnaireTCSDto();
        $questionnaireTCDto->fromQuestionnaireTCS($questionnaireTC);
        $form = $this->createForm(QuestionnaireTCSType::class, $questionnaireTCDto);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $questionnaireTC->hydrateFromDTO($questionnaireTCDto);
            $entityManager->flush();

            return $this->redirectToRoute('app_admin_tcs_questionnaire_tcs_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/tcs/questionnaire_tcs/edit.html.twig', [
            'questionnaire_tcs' => $questionnaireTC,
            'form' => $form,
        ]);
    }

    #[Route('/{id}/show', name: 'app_admin_tcs_questionnaire_tcs_show', methods: ['GET'])]
    public function show(QuestionnaireTCS $questionnaireTC, EntityManagerInterface $entityManager, ExpertRepository $expertRepository, TcsQuestionScoreCalculator $tcsQuestionScoreCalculator): Response
    {

        $changes = false;
        foreach ($questionnaireTC->getQuestionnaireTCSExperts() as $questionnaireTcsExpert) {
            $questionnaireComplete = $questionnaireTcsExpert->questionnaireComplete();
            if ($questionnaireComplete != $questionnaireTcsExpert->isReponduCompletement()) {
                $changes = true;
                $questionnaireTcsExpert->setIsReponduCompletement($questionnaireComplete);
            }
        }
        

        if ($changes) {
            $tcsQuestionScoreCalculator->calculTcsQuestionnaireQuestionsScore($questionnaireTcsExpert->getQuestionnaireTCS());
            $entityManager->flush();
        }

        return $this->render('admin/tcs/questionnaire_tcs/show.html.twig', [
            'questionnaire_tcs' => $questionnaireTC,
            'experts' => $expertRepository->findInnasociatedToQuestionnaire($questionnaireTC)
        ]);
    }

    #[Route('/{id}/delete', name: 'app_admin_tcs_questionnaire_tcs_delete', methods: ['GET', 'POST'])]
    public function delete(Request $request, QuestionnaireTCS $questionnaireTC, EntityManagerInterface $entityManager): Response
    {
        if (!$questionnaireTC->isEditable()) {
            throw $this->createAccessDeniedException();
        }

        if ($this->isCsrfTokenValid('delete'.$questionnaireTC->getId(), $request->request->get('_token'))) {
            $entityManager->remove($questionnaireTC);
            $entityManager->flush();
            return $this->redirectToRoute('app_admin_tcs_questionnaire_tcs_index', [], Response::HTTP_SEE_OTHER);
        }
        return $this->render('admin/tcs/questionnaire_tcs/delete.html.twig', [
            'questionnaire_tcs' => $questionnaireTC,
        ]);
    }

    #[Route('/{id}/duplicate', name: 'app_admin_tcs_questionnaire_tcs_duplicate', methods: ['GET', 'POST'])]
    public function duplicate(QuestionnaireTCS $questionnaireTC, EntityManagerInterface $entityManager): Response
    {
        $questionnaireTC = $questionnaireTC->duplicate();
        $entityManager->persist($questionnaireTC);
        $entityManager->flush();

        $this->flashMessages->addSuccess('admin.tcs.duplicate.success');

        return $this->redirectToRoute('app_admin_tcs_questionnaire_tcs_index');
    }
}
