<?php

namespace Eduprat\AdminBundle\Controller\TCS;

use Doctrine\ORM\EntityManagerInterface;
use <PERSON><PERSON>rat\DomainBundle\Controller\EdupratController;
use Ed<PERSON>rat\DomainBundle\DTO\QuestionnaireTCSSearch;
use Eduprat\DomainBundle\Entity\AuditCategory;
use Ed<PERSON>rat\DomainBundle\Entity\TCS\QuestionnaireTCS;
use Eduprat\DomainBundle\Form\TCS\QuestionnaireTCSSearchType;
use Ed<PERSON>rat\DomainBundle\Repository\TCS\QuestionnaireTCSRepository;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/tcs/questionnaire/archive')]
class ArchiveQuestionnaireTCSController extends EdupratController
{
    #[Route('/search/{page}', name: 'app_admin_tcs_questionnaire_tcs_archive_index')]
    public function index(Request $request, QuestionnaireTCSRepository $questionnaireTCSRepository, EntityManagerInterface $em, int $page = 1): Response
    {
        $thematique = null;
        $label = null;
        if ($request->query->has('label')) {
            $label = $request->query->get('label');
        }
        if ($request->query->has('thematique')) {
            $thematique = $request->query->get('thematique');
            $thematique = $thematique ? $em->getRepository(AuditCategory::class)->find($thematique) : null;
        }
        $search = new QuestionnaireTCSSearch($label, $thematique, true);

        $form = $this->createForm(QuestionnaireTCSSearchType::class, $search);
        $form->handleRequest($request);

        $sortBy = $request->query->get('sortBy');
        $order = $request->query->get('order');

        $nbPerPage = 30;
        $max = 10;
        $count = $questionnaireTCSRepository->countSearchResults($search);
        $questionnaire_tcs = $questionnaireTCSRepository->findSearchResults($search, $page, $nbPerPage, $sortBy, $order);
        $npages = ceil($count / $nbPerPage);
        $current = intval($page);
        $inf = max(1, intval($current - (($max == 1 ? 0 : $max-1) / 2)));
        $sup = min($inf + ($max-1), $npages);
        $pageRange = range($inf, $sup);
        $pagination = array(
            'nb' => $count,
            'page' => $page,
            'npages' => $npages,
            'page_range' => $pageRange,
            'questionnaire_tcs' => $questionnaire_tcs,
        );

        return $this->render('admin/tcs/questionnaire_tcs/archive/index.html.twig', array_merge($pagination, array(
            'form' => $form->createView(),
            'search' => $search,
            'extraParams' => array_merge($search->toArray(), array(
                'sortBy' => $sortBy,
                'order' => $order
            ))
        )));
    }

    #[Route('/{id}/archive', name: 'app_admin_tcs_questionnaire_tcs_archive', methods: ['GET', 'POST'])]
    public function archive(Request $request, QuestionnaireTCS $questionnaireTC, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('archive'.$questionnaireTC->getId(), $request->request->get('_token'))) {
            $questionnaireTC->archive();
            $entityManager->flush();
            return $this->redirectToRoute('app_admin_tcs_questionnaire_tcs_archive_index', [], Response::HTTP_SEE_OTHER);
        }
        return $this->render('admin/tcs/questionnaire_tcs/archive/archive.html.twig', [
            'questionnaire_tcs' => $questionnaireTC,
        ]);
    }

    #[Route('/{id}/unarchive', name: 'app_admin_tcs_questionnaire_tcs_unarchive', methods: ['GET', 'POST'])]
    public function unarchive(Request $request, QuestionnaireTCS $questionnaireTC, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('unarchive'.$questionnaireTC->getId(), $request->request->get('_token'))) {
            $questionnaireTC->unarchive();
            $entityManager->flush();
            return $this->redirectToRoute('app_admin_tcs_questionnaire_tcs_index', [], Response::HTTP_SEE_OTHER);
        }
        return $this->render('admin/tcs/questionnaire_tcs/archive/unarchive.html.twig', [
            'questionnaire_tcs' => $questionnaireTC,
        ]);
    }
}
