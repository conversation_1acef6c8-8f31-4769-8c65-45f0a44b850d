<?php

namespace Eduprat\AdminBundle\Controller;

use <PERSON><PERSON><PERSON>\DomainBundle\Entity\CoordinatorFiles;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Attribute\Route;
use Vich\UploaderBundle\Handler\DownloadHandler;

/**
 * Class CoordinatorFilesController
 */
#[Route(path: '/coordinator_files')]
#[IsGranted('ROLE_COORDINATOR')]
class CoordinatorFilesController extends AbstractController
{
    #[Route(path: '/{id}/file/{fileField}/{n1}', name: 'admin_coordinator_file', defaults: ['n1' => false])]
    public function coordinatorFileDownload(CoordinatorFiles $coordinatorFiles, $fileField, DownloadHandler $downloadHandler, $n1 = false)
    {
        $getter = "get" . ucfirst(str_replace("File", "", $fileField));
        if ($n1) {
            $getter .= 'N1';
            $fileField .= 'N1';
        }
        $filename = call_user_func(array($coordinatorFiles, $getter));
        return $downloadHandler->downloadObject($coordinatorFiles, $fileField, null, $filename, false);
    }

}
