<?php

namespace Eduprat\AdminBundle\Controller;

use Symfony\Component\HttpFoundation\Response;
use Ed<PERSON>rat\AdminBundle\Services\DashboardGenerator;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Person controller.
 */
#[Route(path: '/dashboard')]
class DashboardController extends AbstractController
{

    /**
     * @param $id
     * @return Response
     */
    #[Route(path: '/{id}', name: 'admin_dashboard_show')]
    public function dashboard($id, DashboardGenerator $dashboardGenerator): Response
    {
        return $this->render('admin/dashboard/index.html.twig', array(
            "dashboardUrl" => $dashboardGenerator->getDashboardUrl($this->getUser(), $id)
        ));
    }

}
