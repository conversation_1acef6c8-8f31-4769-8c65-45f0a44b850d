<?php

namespace Eduprat\AdminBundle\Controller;

use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Eduprat\AdminBundle\Services\SuiviCrCalculator;
use ShipMonk\DoctrineEntityPreloader\EntityPreloader;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Doctrine\Common\Collections\ArrayCollection;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Entity\PersonSearch;
use Eduprat\AdminBundle\Http\CsvFileResponse;
use Eduprat\AdminBundle\Services\CoordinatorHonorary;
use Eduprat\AdminBundle\Services\CsvBilanExport;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Model\SessionSearch;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Finder\Finder;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Process\Process;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;

class DefaultController extends EdupratController
{
    #[Route(path: '/', name: 'admin_index')]
    public function index(Request $request)
    {
        /** @var Person $user */
        $user = $this->getUser();

        if ($user->isFormer()) {
            return $this->redirectToRoute('admin_user_monitoring', array('id' => $user->getId()));
        } else if ($user->isCoordinator()) {
            $referer = $request->headers->get("referer");
            if ($referer && (strpos($referer, "login") !== FALSE || strpos($referer, "user") !== FALSE)) {
                if ($user->isCoordinatorLbi()) {
                    return $this->redirectToRoute('admin_formation_index', array('year' => date("Y"), 'archived' => 'non'));
                }
                return $this->redirectToRoute('admin_crm_comptabilite_files', array('id' => $user->getId(), 'year' => date("Y")));
            }
            return $this->redirectToRoute('admin_coordinator_dashboard', array('id' => $user->getId(), 'year' => date("Y")));
        }  else if ($user->isSupervisor() || $user->isSupervisorFr()) {
            return $this->redirectToRoute('admin_suivi_cr', array('person' => $user->getId(), 'year' => date("Y")));
        } else if ($user->isWebmasterCompta()) {
            return $this->redirectToRoute('admin_comptabilite_index', array('year' => date("Y")));
        } else if ($user->isWebmaster()) {
            return $this->redirectToRoute('admin_formation_index', array('year' => date("Y"), 'archived' => 'non'));
        }

        return $this->render('admin/default/index.html.twig');
    }

    #[Route(path: '/classeur-bilan/{year}/{quarter}', name: 'admin_classeur_bilan', defaults: ['quarter' => '0'], requirements: ['quarter' => '^[0-4]$'])]
    public function classeurBilan(EntityManagerInterface $em, $year, string $quarter): Response
    {
        $formations = $em->getRepository(Formation::class)->findByQuarter($year, $quarter);
        $formations = new ArrayCollection($formations);

        $projectDir = $this->getParameter('kernel.project_dir');
        $file = $projectDir . "/uploads/bilan/".sprintf('eduprat_export_%s.csv', $year);
        $fileError = $projectDir . "/uploads/bilan/".sprintf('eduprat_export_%s.log', $year);

        $fileLastUpdate = '';

        if($fileError && file_exists($fileError)) {
            $fileStatus = 'error';
        }
        else {
            if(!$file || !file_exists($file)) {
                $fileStatus = 'generate';
            }
            else if(filesize($file) == 0) {
                $fileStatus = 'generating';
            }
            else {
                $fileStatus = 'generated';
                $fileLastUpdate = date('d/m/Y', filectime($file));
            }
        }

        $total = array(
            "participants" => $this->arrayCollectionSum($formations, function(Formation $f) {
                return sizeof($f->getParticipations());
            }),
            "hours" => $this->arrayCollectionSum($formations, function(Formation $f) {
                return sizeof($f->getParticipations()) * $f->getProgramme()->getDuration();
            }),
            "ca" => $this->arrayCollectionSum($formations, function(Formation $f) { return $f->getCaTotal();})
        );

        return $this->render('admin/default/classeur_bilan.html.twig', array(
            'first_year' => $em->getRepository(Formation::class)->findFirst()->getStartDate()->format('Y'),
            'formations' => $formations,
            'year' => $year,
            'quarter' => $quarter,
            'total' => $total,
            'fileStatus' => $fileStatus,
            'fileLastUpdate' => $fileLastUpdate
        ));
    }

    /**
     * @param Person $person
     * @param                                    $year
     */
    #[Route(path: '/suivi-cr/{person}/{year}', name: 'admin_suivi_cr')]
    public function suiviCr(Person $person, EntityManagerInterface $em, $year): Response
    {
        if (!$person->isSupervisor() && !$person->isSupervisorFr()) {
            throw $this->createNotFoundException();
        }

        $participationRepository = $em->getRepository(Participation::class);
        $personRepository = $em->getRepository(Person::class);

        if ($person->isSupervisorFr()) {
            $personSearch = new PersonSearch();
            $personSearch->setRole(Person::ROLE_COORDINATOR);
            $personSearch->setYear($year);
            $coordinators = $personRepository->findAllSearchResultsObName($personSearch);
            $person->setCoordinatorsPerson($coordinators);
        }

        $coordinatorsTotalCaSecteurByCoordinateur = $personRepository->findSupervisorCA($person, $year) + $personRepository->findSupervisorCA($person, $year, true);
        return $this->render('admin/default/suivi_cr.html.twig', array(
            'first_year' => $em->getRepository(Formation::class)->findFirst()->getStartDate()->format('Y'),
            'coordinatorsTotalCaSecteurByCoordinateur' => $coordinatorsTotalCaSecteurByCoordinateur,
            'supervisor' => $person,
            'year' => $year,
            'counts' => $participationRepository->findCountsForSupervisor($person, $year),
            'newHonorary' => new \DateTime("$year-01-01") >= new \DateTime($this->getParameter('honoraires.migration_date'))
        ));
    }

    /**
     * @param Person $person
     * @param                                    $year
     */
    #[Route(path: '/suivi-cr/{person}/{year}/async/{field}', name: 'admin_suivi_cr_async')]
    public function suiviCrAsync(Person $person, EntityManagerInterface $em, SuiviCrCalculator $calculator, $year, $field)
    {
        $value = 0;
        if ($field === "coordinatorsTotalFormerHonorary") {
            $value = $calculator->calculCoordinatorsTotalFormerHonorary($person, $year);
        } elseif ($field === "coordinatorsTotalRestaurationHonoraryByCoordinator") {
            $value = $calculator->calculCoordinatorsTotalRestaurationHonoraryByCoordinator($person, $year);
        } else {
            if ($person->isSupervisorFr()) {
                $supervisors = $em->getRepository(Person::class)->findSupervisors();
                foreach($supervisors as $s) {
                    $value += round(call_user_func(array($s, "get" . ucfirst($field)), $year), 2);
                }
            } else {
                $value = round(call_user_func(array($person, "get" . ucfirst($field)), $year), 2);
            }
        }
        return new JsonResponse(array(
            "value" => $value
        ));
    }

    function arrayCollectionSum(Collection $collection, $getter) {
        return array_reduce(
            $collection->map($getter)->toArray(),
            function($a, $b) { $a += $b; return $a; }
        );
    }

    /**
     * @param Person $person
     * @param                                    $year
     */
    #[Route(path: '/suivi-cr/{person}/{year}/{coordinator}', name: 'admin_suivi_cr_detail')]
    public function suiviCrDetail(Person $person, $year, Person $coordinator, EntityManagerInterface $em, CoordinatorHonorary $coordinatorHonoraryService): Response
    {
        if (!$person->isSupervisor() && !$person->isSupervisorFr()) {
            throw $this->createNotFoundException();
        }

        $budgetCRs = array();
        $total = array();

        if (is_null($coordinator)) {
            $coordinator = $person->getCoordinatorsPerson()->first();
        }

        $coordinatorRepo = $em->getRepository(Coordinator::class);

        $sessionSearch = new SessionSearch();
        $sessionSearch->searchStartDate = (new \DateTime())->createFromFormat('Y-m-d', $year.'-01-01');
        $sessionSearch->searchEndDate = (new \DateTime())->createFromFormat('Y-m-d', $year.'-12-31');
        $sessionSearch->archived = "non";
        $formations = $em->getRepository(Formation::class)->findSearchResults($sessionSearch, 1, 1000, $coordinator);
        $arrayIdSessionCoordinator = $coordinatorRepo->findCoordinatorByPersonYear($coordinator, $year);

        $preloader = new EntityPreloader($em);
        $preloader->preload([$coordinator], 'coordinators');
        $preloader->preload($formations, 'participations');

        $newHonorary = new \DateTime("$year-01-01") >= new \DateTime($this->getParameter('honoraires.migration_date'));

        /** @var Formation $formation */
        foreach ($formations as $key => $formation) {
            if (!isset($arrayIdSessionCoordinator[$formation->getId()])) {
                continue;
            }
            $budgetCRs[$formation->getId()]['coordinator'][$coordinator->getId()] = $arrayIdSessionCoordinator[$formation->getId()];

            $budgetCRs[$formation->getId()]['honorary'][$coordinator->getId()] = $coordinatorHonoraryService->calculTotalHonorary($formation, $budgetCRs[$formation->getId()]['coordinator'][$coordinator->getId()], false, true, false);
            $budgetCRs[$formation->getId()]['displayPDF'][$coordinator->getId()] = $coordinatorHonoraryService->displayCoordinatorsHonoraryByProgramme($formation);
            if(!isset($total[$coordinator->getId()])) {
                $total[$coordinator->getId()] = 0;
            }
            $total[$coordinator->getId()] += $budgetCRs[$formation->getId()]['honorary'][$coordinator->getId()]['total'];
        }

        return $this->render('admin/default/suivi_cr_detail.html.twig', array(
            'supervisor' => $person,
            'coordinator' => $coordinator,
            'year' => $year,
            'budgetCRs' => $budgetCRs,
            'total' => $total,
            'newHonorary' => $newHonorary,
        ));
    }

    /**
     * @param Request $request
     * @return BinaryFileResponse|RedirectResponse
     */
    #[Route(path: '/download-models', name: 'admin_download_models')]
    public function downloadModels(Request $request)
    {
        $finder = new Finder();
        $files = $finder->files()->in($this->getParameter('formation.models.upload_destination'))->name('*.zip');

        if (!$files->count()) {
            $this->flashMessages->addWarning('Aucun modèle de facture existant');
            $referer = $request->headers->get('referer');
            return $this->redirect($referer);
        }

        $iterator = $files->getIterator();
        $iterator->rewind();
        $firstFile = $iterator->current();

        return new BinaryFileResponse($firstFile);
    }

    /**
     * @param $year
     * @return CsvFileResponse
     */
    #[Route(path: '/dashboard-csv/{year}', name: 'admin_dashboard_csv')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function csvDashboard(CsvBilanExport $csvBilanExport, $year = null): CsvFileResponse {
        $data = $csvBilanExport->export($year);
        return new CsvFileResponse($data, sprintf('eduprat_export_%s.csv', $year));
    }

    /**
     * @param $year
     */
    #[Route(path: '/bilan-generate-csv/{year}', methods: ['GET'], name: 'bilan_generate_csv')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function bilanGenerateCsv($year): JsonResponse
    {
        $command = sprintf("eduprat:csv %s", $year);

        $projectDir = $this->getParameter('kernel.project_dir');
        $cmd = sprintf('php %s/bin/console %s', $projectDir, $command);
        $cmd = sprintf("%s --env=%s >/dev/null 2>&1 &", $cmd, $this->getParameter('kernel.environment'));

        $process = Process::fromShellCommandline($cmd);
        $process->run();
        if (!$process->isSuccessful()) {
            throw new \RuntimeException($process->getErrorOutput());
        }

        $pid = $process->getOutput();

        return new JsonResponse(["status" => "ok", "pid" => $pid]);
    }

    /**
     * @param $year
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/csv-file-get/{year}', methods: ['GET'], name: 'csv_file_get')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function csvFileGet($year): BinaryFileResponse
    {
        $filename = sprintf('eduprat_export_%s.csv', $year);
        $projectDir = $this->getParameter('kernel.project_dir');
        $file = $projectDir . "/uploads/bilan/".$filename;

        if($file && file_exists($file) && filesize($file) > 0) {
            $response = new BinaryFileResponse($file);

            $response->setContentDisposition(
                ResponseHeaderBag::DISPOSITION_ATTACHMENT,
                $filename);

            return $response;
        }
        else {
            throw new NotFoundHttpException();
        }
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/donnees-personnelles', name: 'admin_personal_data')]
    public function personalData(): Response
    {
        return $this->render('admin/default/personal-data.html.twig');
    }
}
