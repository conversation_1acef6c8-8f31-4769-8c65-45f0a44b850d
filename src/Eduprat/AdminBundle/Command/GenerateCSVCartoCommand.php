<?php

namespace Eduprat\AdminBundle\Command;

use Eduprat\AdminBundle\Services\CsvBilanExport;
use Ed<PERSON>rat\DomainBundle\Services\Ftp;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand('eduprat:csv_carto')]
class GenerateCSVCartoCommand extends Command
{
    /**
     * @var string
     */
    private $projectDir;
    /**
     * @var CsvBilanExport
     */
    private $csvBilanExport;
    /**
     * @var FTP
     */
    private $ftp;

    public function __construct(string $projectDir, CsvBilanExport $csvBilanExport, Ftp $ftp)
    {
        parent::__construct();
        $this->projectDir = $projectDir;
        $this->csvBilanExport = $csvBilanExport;
        $this->ftp = $ftp;
    }

    protected function configure(): void
    {
        $this
            ->setDescription("Génère le fichier CSV pour la carto")
            ;
    }

    protected function initialize(InputInterface $input,OutputInterface $output): void
    {
        parent::initialize($input, $output);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {

        $date = (new \DateTime())->format('Y-m-d');
        $fileTmp = $this->projectDir . "/uploads/carto/".sprintf('eduprat_export_%s-tmp.csv', $date);
        $file = $this->projectDir . "/uploads/carto/".sprintf('eduprat_export_%s.csv', $date);

        if($file && file_exists($file)) {
            unlink($file);
        }

        touch($file);

        $this->csvBilanExport->exportCarto($fileTmp);

        rename($fileTmp, $file);

        // Envoi du fichier
        if (!$this->ftp->connect()) {
            throw new \Exception('Connexion FTP impossible');
        }
        if (!$this->ftp->upload($file, "/" . "clients.csv", FTP_BINARY)) {
            throw new \Exception('Erreur lors de l\'upload');
        }
        // Suppression du fichier
        unlink($file);
        return Command::SUCCESS;
    }
}
