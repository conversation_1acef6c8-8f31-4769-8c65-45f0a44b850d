<?php

namespace Eduprat\AdminBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Eduprat\DomainBundle\Entity\Participation;

#[AsCommand(name:'eduprat:participation-coordinators', description: "Associe l'unique coordinateur de la formation à la participation si elle n'a pas de coordinateur associé")]
class associateEmptyParticipationCoordinatorCommand extends Command
{

    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
        parent::__construct();
    }

    protected function configure(): void
    {
    }

    protected function initialize(InputInterface $input,OutputInterface $output): void
    {
        parent::initialize($input, $output);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {

        $repo = $this->entityManager->getRepository(Participation::class);
        $participations = $repo->findWithOneCoordinator();

        foreach($participations as $participation) {
            $coodinator = null;
            foreach($participation->getFormation()->getCoordinators() as $c) {
                $coodinator = $c;
            }
            $participation->setCoordinator($coodinator);
            if ($participation == $participation->getParticipant()->getlastParticipation()) {
                $participation->getParticipant()->setCoordinator($coodinator->getPerson());
            }
            $this->entityManager->persist($participation);
        }
        $this->entityManager->flush();
        return Command::SUCCESS;
    }

}
