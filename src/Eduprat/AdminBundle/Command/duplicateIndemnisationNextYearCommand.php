<?php

namespace Eduprat\AdminBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Eduprat\DomainBundle\Entity\Indemnisation;
use Eduprat\DomainBundle\Entity\IndemnisationPriseEnCharge;
use Eduprat\DomainBundle\Entity\PriseEnCharge;

#[AsCommand(name: 'eduprat:indemnisation-duplicate', description: "Initialisation de l'année des indemnisations déjà créées")]
class duplicateIndemnisationNextYearCommand extends Command
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
        parent::__construct();
    }

    protected function configure(): void
    {
    }

    protected function initialize(InputInterface $input,OutputInterface $output): void
    {
        parent::initialize($input, $output);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $todayYear = date('Y');
        $indemnisations = $this->entityManager
            ->createQueryBuilder()
            ->select('c')
            ->from(Indemnisation::class, 'c')
            ->where('c.year = :year')->setParameter('year', $todayYear)
            ->getQuery()
            ->getResult()
            ;

        $priseEnCharges = $this->entityManager->getRepository(PriseEnCharge::class)->findBy(array(), array('name' => 'ASC'));


        foreach($indemnisations as $indemnisation) {
            $newIndemnisation = clone $indemnisation;
            $newIndemnisation->setYear($todayYear + 1);
            $newIndemnisation->resetPriseEnCharges();

            foreach($priseEnCharges as $priseEnCharge) {
                $newIndemnisationPriseEnCharge = new IndemnisationPriseEnCharge($newIndemnisation, $priseEnCharge);
                if ($indemnisation->getUniquePriseEnCharge() && $priseEnCharge == $indemnisation->getUniquePriseEnCharge()->getPriseEnCharge()) {
                    $newIndemnisationPriseEnCharge->setPrice($indemnisation->getUniquePriseEnCharge()->getPrice());
                }
                $newIndemnisation->addPriseEnCharge($newIndemnisationPriseEnCharge);
            }

            $this->entityManager->persist($newIndemnisation);
        }
        $this->entityManager->flush();
        return Command::SUCCESS;
    }
}
