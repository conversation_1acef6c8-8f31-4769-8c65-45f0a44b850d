<?php

namespace Eduprat\AdminBundle\Model;

use Doctrine\Common\Collections\ArrayCollection;


/**
 * Entité pour moteur de recherche
 */
class AbstractSearch
{
    const SEARCH_FORM = null;

    const FIELDS_REPLACER = array();

    const FIELDS_REPLACER_MAPPING = array();

    public ?string $sortBy = null;
    public ?string $order = null;
    public ?int $limit = null;

    public function toArray() {
        $reflection = new \ReflectionClass($this);
        $values = array();
        foreach ($reflection->getProperties() as $property) {
            $name = $property->getName();
            $values[$name] = $this->$name;
        }
        return $values;
    }

    public function handleArray(array $parameters) {
        $values = array();
        foreach ($this->toArray() as $name => $value) {
            if (isset($parameters[$name]) && !empty($parameters[$name])) {
                $values[$name] = $parameters[$name];
            }
        }
        return $values;
    }

    public function getParams() {
        $params = [];
        $reflectionClass = new \ReflectionClass($this);
        $exluded = array_map(function($f) {
            return $f . "Name";
        }, $reflectionClass->getConstant("FIELDS_REPLACER"));
        foreach ($reflectionClass->getProperties() as $property) {
            $name = $property->getName();
            if ($this->{$name}) {
                if (in_array($name, $exluded)) {
                    continue;
                }
                $params[$name] = $this->{$name};
                if (is_object($this->{$name}) && method_exists($this->{$name}, "getId")) {
                    $params[$name] = $this->{$name}->getId();
                }
                if($this->{$name} instanceof ArrayCollection) {
                    $params[$name] = $this->{$name}->map(function($item) {
                        return $item->getId();
                    })->toArray();
                }
                if ($this->{$name} instanceof \DateTime) {
                    $params[$name] = $this->{$name}->format("Y-m-d");
                }
            }

        }
        return $params;
    }

    public function isValid() {
        return !empty($this->getParams());
    }

    public function prepareFormOptions($options = array(), $parameters = array()) {
        return $options;
    }

    public function prepareSearch($parameters = array()) {}

    public function transform() {
        foreach ((new \ReflectionClass($this))->getProperties() as $property) {
            $value = $this->{$property->getName()};
            if (is_object($value) && method_exists($value, "getId")) {
                $this->{$property->getName()} = $this->{$property->getName()}->getId();
            }
        }
    }

}