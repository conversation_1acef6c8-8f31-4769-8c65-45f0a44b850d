<?php

namespace Eduprat\AdminBundle\Model;
use Symfony\Component\HttpFoundation\Request;


/**
 * Entité pour moteur de recherche
 */
class EvaluationUserSearch
{

    /**
     * @var string
     */
    private $firstname;

    /**
     * @var string
     */
    private $lastname;

    /**
     * @var string
     */
    private $role;

    /**
     * @var \DateTime
     */
    private $start;

    /**
     * @var \DateTime
     */
    private $end;

    /**
     * @return string
     */
    public function getFirstname()
    {
        return $this->firstname;
    }

    /**
     * @param string $firstname
     * @return EvaluationUserSearch
     */
    public function setFirstname($firstname)
    {
        $this->firstname = $firstname;

        return $this;
    }

    /**
     * @return string
     */
    public function getLastname()
    {
        return $this->lastname;
    }

    /**
     * @param string $lastname
     * @return EvaluationUserSearch
     */
    public function setLastname($lastname)
    {
        $this->lastname = $lastname;

        return $this;
    }

    /**
     * @return string
     */
    public function getRole()
    {
        return $this->role;
    }

    /**
     * @param string $role
     * @return EvaluationUserSearch
     */
    public function setRole($role)
    {
        $this->role = $role;

        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getStart()
    {
        return $this->start;
    }

    /**
     * @param \DateTime $start
     * @return EvaluationUserSearch
     */
    public function setStart($start)
    {
        $this->start = $start;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getEnd()
    {
        return $this->end;
    }

    /**
     * @param \DateTime $end
     * @return EvaluationUserSearch
     */
    public function setEnd($end)
    {
        $this->end = $end;
        return $this;
    }

    public function toArray() {
        return array(
            'firstname' => $this->getFirstname(),
            'lastname' => $this->getLastname(),
            'role' => $this->getRole(),
            'start' => $this->getStart() !== null ? $this->getStart()->format('Y-m-d') : null,
            'end' => $this->getEnd() !== null ? $this->getEnd()->format('Y-m-d') : null,
        );
    }

    public function getParams() {
        $params = [];
        foreach ($this->toArray() as $name => $value) {
            if ($value) {
                $params[$name] = $value;
            }
        }
        return $params;
    }

    public function isValid() {
        return !empty($this->getParams());
    }

    public function handleRequest(Request $request) {
        foreach ($this->toArray() as $name => $value) {
            if ($request->query->has($name)) {
                if (in_array($name, array('start', 'end'))) {
                    $this->$name = new \DateTime($request->query->get($name));
                }
                else {
                    $this->$name = $request->query->get($name);
                }
            }
        }
    }

}