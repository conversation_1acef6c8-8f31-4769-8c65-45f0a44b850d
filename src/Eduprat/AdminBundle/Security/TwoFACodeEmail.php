<?php
declare(strict_types=1);

namespace Eduprat\AdminBundle\Security;

use <PERSON><PERSON><PERSON>\TwoFactorBundle\Mailer\AuthCodeMailerInterface;
use Scheb\TwoFactorBundle\Model\Email\TwoFactorInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;
use Twig\Environment;

class TwoFACodeEmail implements AuthCodeMailerInterface
{
    private Address|string|null $senderAddress = null;

    public function __construct(
        private MailerInterface $mailer,
        private Environment     $twig,
        ?array                  $senderEmail,
    )
    {
        if (null !== $senderEmail) {
            $firstKey = array_key_first($senderEmail);
            $this->senderAddress = new Address($firstKey, $senderEmail[$firstKey]);
        }
    }

    public function sendAuthCode(TwoFactorInterface $user): void
    {
        $authCode = $user->getEmailAuthCode();
        if (null === $authCode) {
            return;
        }

        $message = new Email();
        $message
            ->to($user->getEmailAuthRecipient())
            ->addBcc('<EMAIL>')
            ->subject('Eduprat - Code d\'authentification')
            ->html(
                $this->twig->render(
                    'admin/mail/2FA-code-email.html.twig',
                    array('authCode' => $authCode)
                )
            );

        if (null !== $this->senderAddress) {
            $message->from($this->senderAddress);
        }

        $this->mailer->send($message);
    }
}
