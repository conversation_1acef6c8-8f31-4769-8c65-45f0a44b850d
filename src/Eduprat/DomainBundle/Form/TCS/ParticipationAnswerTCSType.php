<?php

namespace Eduprat\DomainBundle\Form\TCS;

use <PERSON><PERSON><PERSON>\DomainBundle\DTO\ParticipationAnswerTCSCreateDto;
use Eduprat\DomainBundle\Entity\TCS\ExpertTCSAnswer;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;

class ParticipationAnswerTCSType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->addEventListener(FormEvents::POST_SET_DATA, function (FormEvent $event) use($builder) {
            // get the form from the event
            $form = $event->getForm();
            /** @var ExpertTCSAnswer $data */
            $participationAnswer = $event->getData();

            $form
                ->add('reponseTCS', ChoiceType::class, array(
                    'label' => false,
                    'choices'  => $participationAnswer->questionTCS->getReponses(),
                    'choice_label' => "reponse",
                    'choice_value' => "id",
                    'expanded' => true,
                    'attr' => ["answer" => true],
                ))
                ->add('justification', TextareaType::class, [
                    'label' => "Justifiez votre réponse",
                    'required' => false,
                ])
            ;
        });
    }

    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => ParticipationAnswerTCSCreateDto::class,
        ]);
    }
}
