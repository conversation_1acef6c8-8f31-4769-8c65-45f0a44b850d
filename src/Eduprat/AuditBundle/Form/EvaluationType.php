<?php

namespace Eduprat\AuditBundle\Form;

use Ed<PERSON>rat\DomainBundle\Entity\EvaluationQuestion;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\FormView;
use Symfony\Component\OptionsResolver\OptionsResolver;

class EvaluationType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        /**
         * @var EvaluationQuestion $question
         */
        foreach ($options['questions'] as $index => $question) {
            $builder->add($question->getIndex(), ChoiceType::class, array(
                'label' => $question->getLabel(),
                "expanded" => true,
                "multiple" => false,
                "choices" => [1 => 1, 2 => 2, 3 => 3, 4 => 4, 5 => 5],
                'label_attr' => array('class' => 'radio-inline')
            ));
        }
    }

    /**
     * @inheritDoc
     */
    public function finishView(FormView $view, FormInterface $form, array $option): void {
        foreach ($view as $key => $childView) {
            if (isset($options['questions'][$key - 1])) {
                $childView->vars['min'] = $options['questions'][$key - 1]->getMinLabel();
                $childView->vars['max'] = $options['questions'][$key - 1]->getMaxLabel();
            }
        }
    }


    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            "questions" => []
        ));
    }

}
