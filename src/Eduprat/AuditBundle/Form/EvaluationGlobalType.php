<?php

namespace Eduprat\AuditBundle\Form;

use Ed<PERSON>rat\DomainBundle\Entity\EvaluationGlobalQuestion;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\FormView;
use Symfony\Component\OptionsResolver\OptionsResolver;

class EvaluationGlobalType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        /**
         * @var EvaluationGlobalQuestion $question
         */
        foreach ($options['questions'] as $index => $question) {
            if ($question->getType() === EvaluationGlobalQuestion::TYPE_NOTE) {
                $builder->add($question->getIndex(), ChoiceType::class, array(
                    'required' => true,
                    'label' => $question->getLabel(),
                    "expanded" => true,
                    "multiple" => false,
                    "choices" => [1 => 1, 2 => 2, 3 => 3, 4 => 4, 5 => 5],
                    'label_attr' => array('class' => 'radio-inline')
                ));
            } else if ($question->getType() === EvaluationGlobalQuestion::TYPE_NOTE_OPTIONAL) {
                $builder->add($question->getIndex(), ChoiceType::class, array(
                    'required' => true,
                    'label' => $question->getLabel(),
                    "expanded" => true,
                    "multiple" => false,
                    "choices" => [1 => 1, 2 => 2, 3 => 3, 4 => 4, 5 => 5, "Non applicable" => 0],
                    'label_attr' => array('class' => 'radio-inline')
                ));
            } else if ($question->getType() === EvaluationGlobalQuestion::TYPE_RADIO) {
                $builder->add($question->getIndex(), ChoiceType::class, array(
                    'required' => true,
                    'label' => $question->getLabel(),
                    "expanded" => true,
                    "multiple" => false,
                    "choices" => ["Oui" => 1, "Non" => 0],
                    'label_attr' => array('class' => 'radio-inline radio-yes-no')
                ));
            } else if ($question->getType() === EvaluationGlobalQuestion::TYPE_TEXTAREA) {
                $builder->add($question->getIndex(), TextareaType::class, array(
                    'label' => $question->getLabel(),
                    'required' => false,
                    'attr' => array(
                        'rows' => 5
                    )
                ));
            }
        }

    }

    /**
     * @inheritDoc
     */
    public function finishView(FormView $view, FormInterface $form, array $option): void {
        $titles = array();
        foreach ($view as $key => $childView) {
            $identifier = explode("_", $childView->vars['name'])[0];
            if (!in_array($identifier, $titles)) {
                $titles[] = $identifier;
                $childView->vars['title'] = $identifier;
            }
        }
    }


    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            "questions" => [],
            "step" => 1,
            "manager" => null
        ));
    }

}
