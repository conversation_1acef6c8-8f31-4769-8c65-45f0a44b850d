<?php

namespace Eduprat\AuditBundle\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\ParticipantDateDownload;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\ParticipationLog;
use Eduprat\DomainBundle\Services\ParticipationLogger;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

use Eduprat\AuditBundle\Services\CourseManager;

/**
 * Class ParticipantDateDownloadController
 */
#[Route(path: '/participant-date-download')]
class ParticipantDateDownloadController extends AbstractController
{
    /**
     * @param Request $request
     * @param Participation $participation
     * @param string $type
     * @param ParticipationLogger $participationLogger
     * @return JsonResponse
     */
    #[Route(path: '/{participation}/{type}', methods: ['POST'], name: 'participant_date_download')]
    public function edit(Participation $participation, $type, ParticipationLogger $participationLogger, EntityManagerInterface $em): JsonResponse
    {
        $participant = $participation->getParticipant();
        $formation = $participation->getFormation();

        $participantDateDownloadRepo = $em->getRepository(ParticipantDateDownload::class);
        $participantDateDownload = $participantDateDownloadRepo->findOneBy(array(
            'formation' => $formation,
            'participant' => $participant
        ));

        if(!$participantDateDownload) {
            $participantDateDownload = new ParticipantDateDownload();
            $participantDateDownload->setFormation($formation);
            $participantDateDownload->setParticipant($participant);
        }

        if($type !== 'topo') {
            $setter = "set" . ucfirst($type);
            if (method_exists($participantDateDownload, $setter)) {
                call_user_func(array($participantDateDownload, $setter), $participation->getId());
                call_user_func(array($participantDateDownload, $setter . 'DownloadedAt'), new \DateTime());
            }
        }
        else {
            $setter = "set" . ucfirst($type) . 's';
            $topos = $participantDateDownload->getTopos();
            $toposDownloadedAt = $participantDateDownload->getToposDownloadedAt();

            $topos[$participation->getId()] = $participation->getId();
            $toposDownloadedAt[$participation->getId()] = new \DateTime();

            if (method_exists($participantDateDownload, $setter)) {
                call_user_func( array($participantDateDownload, $setter), $topos);
                call_user_func( array($participantDateDownload, $setter.'DownloadedAt'), $toposDownloadedAt);
            }
        }

        $em->persist($participantDateDownload);

        $participation = $em->getRepository(Participation::class)->findOneBy(array(
            'formation' => $formation,
            'participant' => $participant
        ));

        if ($participation) {
            $logType = null;
            $step = "";
            switch ($type) {
                case "prerestitution":
                    $logType = ParticipationLog::ACTION_DOWNLOAD_PRERESTITUTION;
                    $step = CourseManager::STEP2;
                    break;
                case "restitution":
                    $logType = ParticipationLog::ACTION_DOWNLOAD_RESTITUTION;
                    $step = CourseManager::STEP4;
                    break;
                case "topo":
                    $logType = ParticipationLog::ACTION_DOWNLOAD_TOPO;
                    $step = CourseManager::STEP2;
                    break;
                case "action":
                    $logType = ParticipationLog::ACTION_DOWNLOAD_ACTION;
                    $step = CourseManager::STEP2;
                    break;
                case "attestation":
                    $logType = ParticipationLog::ACTION_DOWNLOAD_ATTESTATION;
                    break;
                case "attestation_horaire":
                    $logType = ParticipationLog::ACTION_DOWNLOAD_ATTESTATION_HORAIRE;
                    break;
                case "attestation_honneur":
                    $logType = ParticipationLog::ACTION_DOWNLOAD_ATTESTATION_HONNEUR;
                    break;
            }
            if ($logType) {
                $participationLogger->addLog($participation, $logType, 0, null, null, $step);
            }
        }

        $em->flush();

        return new JsonResponse(array("status" => "ok"));
    }
}
