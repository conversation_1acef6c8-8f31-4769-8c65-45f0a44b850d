<?php

namespace Eduprat\AuditBundle\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\DomainBundle\DTO\ParticipationAnswerTCSCreateDto;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\TCS\ParticipationAnswerTCS;
use Eduprat\DomainBundle\Entity\TCS\ParticipationGroupeQuestionTCS;
use Eduprat\DomainBundle\Entity\TCS\QuestionTCS;
use Doctrine\Common\Collections\ArrayCollection;
use Eduprat\DomainBundle\Form\TCS\ParticipationAnswerTCSType;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Eduprat\DomainBundle\Entity\ParticipationLog;
use Eduprat\DomainBundle\Services\ParticipationLogger;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Alienor\ApiBundle\Services\FlashMessages;


#[Route('/participation')]
class ParticipationTcsAnswerController extends AbstractController
{
    private CourseManager $courseManager;
    private ParticipationLogger $participationLogger;
    private FlashMessages $flashMessages;

    public function __construct(CourseManager $courseManager, ParticipationLogger $participationLogger, FlashMessages $flashMessages)
    {
        $this->courseManager = $courseManager;
        $this->participationLogger = $participationLogger;
        $this->flashMessages = $flashMessages;
    }

    #[Route('/{participation}/{questionTCS}/tcs_answer', name: 'app_admin_tcs_participation_answer', methods: ['GET', 'POST'])]
    public function answerTcsQuestionGroup(Request $request, Participation $participation, QuestionTCS $questionTCS, EntityManagerInterface $entityManager): Response
    {

        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        if ($participation->hasAlreadyAnswerTo($questionTCS)) {
            return $this->redirectToRoute('app_admin_tcs_participation_answer_bilan', [
                "participation" => $participation->getId(),
                "questionTCS" => $questionTCS->getId(),
            ]);
        }
        if (!$participation->canAnswerTo($questionTCS)) {
            throw $this->createAccessDeniedException();
        }

        $questionnaireTcs = $questionTCS->getGroupeQuestion()->getQuestionnaireTCS();
        $logKey = sprintf("p_log_tcs_%s_%s", $participation->getId(), $questionnaireTcs->getId());
        $logAction = ParticipationLog::ACTION_FORM_PRE;

        $currentModule = CourseManager::STEP1_FORM_PRE_LABEL;
        $this->participationLogger->startLog($participation, $logAction, $logKey, $participation->isStepCompleted($currentModule) ? 100 : 0, $participation->isStepCompleted($currentModule) ? "100" : "0");

        $participationAnswerTCSCreateDto = new ParticipationAnswerTCSCreateDto($participation, $questionTCS);
        $form = $this->createForm(ParticipationAnswerTCSType::class, $participationAnswerTCSCreateDto);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $this->participationLogger->endLog($participation, $logAction, $logKey, 0, "0");
            $entityManager->persist(ParticipationAnswerTCS::createFromDTO($participationAnswerTCSCreateDto));
            $entityManager->flush();

            return $this->redirectToRoute('app_admin_tcs_participation_answer_bilan', [
                "participation" => $participation->getId(),
                "questionTCS" => $questionTCS->getId(),
            ]);
        }

        $course = $this->courseManager->getCourseDetail($participation);
        $step = $course[CourseManager::STEP1];

        return $this->render('audit/module/participation/participation_answer.html.twig', [
            'questionnaireTCS' => $questionnaireTcs,
            'question' => $questionTCS,
            'form' => $form,
            'step' => $step,
            'course' => $course,
            'participation' => $participation,
            'current_module' => $currentModule,
            'courseManager' => $this->courseManager,
            'previous_question' => $questionnaireTcs->getPreviousQuestion($questionTCS),
        ]);
    }

    #[Route('/{participation}/{questionTCS}/tcs_resultat', name: 'app_admin_tcs_participation_answer_bilan', methods: ['GET'])]
    public function resultQuestionTCS(Participation $participation, QuestionTCS $questionTCS, EntityManagerInterface $entityManager): Response
    {

        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $participationAnswerTCS = $entityManager->getRepository(ParticipationAnswerTCS::class)->findOneBy(['participation' => $participation, 'questionTCS' => $questionTCS]);
        $course = $this->courseManager->getCourseDetail($participation);
        $step = $course[CourseManager::STEP1];

        $questionnaireTcs = $questionTCS->getGroupeQuestion()->getQuestionnaireTCS();
        $logKey = sprintf("p_log_tcs_%s_%s", $participation->getId(), $questionnaireTcs->getId());
        $logAction = ParticipationLog::ACTION_FORM_PRE;

        $currentModule = CourseManager::STEP1_FORM_PRE_LABEL;
        $this->participationLogger->startLog($participation, $logAction, $logKey, $participation->isStepCompleted($currentModule) ? 100 : 0, $participation->isStepCompleted($currentModule) ? "100" : "0");

        return $this->render('audit/module/participation/participation_bilan.html.twig', [
            'questionnaireTCS' => $questionnaireTcs,
            'question' => $questionTCS,
            'participationAnswerTCS' => $participationAnswerTCS,
            'participation' => $participation,
            'step' => $step,
            'course' => $course,
            'current_module' => $currentModule,
            'courseManager' => $this->courseManager,
            'previous_question' => $questionnaireTcs->getPreviousQuestion($questionTCS),
        ]);
    }

    #[Route('/{participation}/{questionTCS}/tcs_next_question', name: 'app_admin_tcs_participation_next_question', methods: ['GET'])]
    public function nextQuestion(Participation $participation, QuestionTCS $questionTCS, EntityManagerInterface $entityManager): RedirectResponse
    {

        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $nextQuestion = $participation->getFormation()->getQuestionnaireTCS()->getNextQuestion($questionTCS);
        if ($nextQuestion === null) {
            $this->courseManager->completeCourseStep($participation,CourseManager::STEP1_FORM_PRE_LABEL);
            $participation->setCompletedForm1(true);
            $entityManager->persist($participation);
            $entityManager->flush();
           
            $questionnaireTcs = $questionTCS->getGroupeQuestion()->getQuestionnaireTCS();
            $logKey = sprintf("p_log_tcs_%s_%s", $participation->getId(), $questionnaireTcs->getId());
            $logAction = ParticipationLog::ACTION_FORM_PRE;
            $this->participationLogger->endLog($participation, $logAction, $logKey, 100, "100");

            return $this->redirectToRoute('eduprat_front_next_module', [
                "participation" => $participation->getId(),
                "module" => CourseManager::STEP1_FORM_PRE_LABEL,
            ]);
        }
        return $this->redirectToRoute('app_admin_tcs_participation_answer', [
            "participation" => $participation->getId(),
            "questionTCS" => $nextQuestion->getId(),
            'courseManager' => $this->courseManager,
        ]);
    }

    // Accès à la première question non répondue du questionnaire
    // Si toutes les questions sont répondues, accès bilan de la première question
    #[Route('/{participation}/acces_tcs', name: 'app_admin_tcs_participation_acces_tcs', methods: ['GET'])]
    public function accesTcs(Participation $participation, EntityManagerInterface $entityManager): RedirectResponse
    {

        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $participationQuestions = $entityManager->getRepository(QuestionTCS::class)->findByParticipation($participation);
        $nextQuestion = $participation->getFormation()->getQuestionnaireTCS()->getNextQuestionNotAnswered(new ArrayCollection($participationQuestions));
        if ($nextQuestion === null) {
            return $this->redirectToRoute('app_admin_tcs_participation_answer_bilan', [
                "participation" => $participation->getId(),
                "questionTCS" => $participation->getFormation()->getQuestionnaireTCS()->getFirstQuestion()->getId(),
            ]);
        }
        return $this->redirectToRoute('app_admin_tcs_participation_answer', [
            "participation" => $participation->getId(),
            "questionTCS" => $nextQuestion->getId(),
            'courseManager' => $this->courseManager,
        ]);
    }

    #[Route('/{participation}/{questionTCS}/tcs_synthese_educative', name: 'app_admin_tcs_participation_synthese_educative', methods: ['GET', 'POST'])]
    public function syntheseEducativeTCS(Request                $request,
                                         Participation          $participation,
                                         QuestionTCS            $questionTCS,
                                         EntityManagerInterface $entityManager
    ): Response
    {

        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        if (!$participation->hasAnswerToAll($questionTCS->getGroupeQuestion())) {
            throw $this->createAccessDeniedException();
        }

        $currentModule = CourseManager::STEP1_FORM_PRE_LABEL;
        $course = $this->courseManager->getCourseDetail($participation);
        $step = $course[CourseManager::STEP1];

        $questionnaireTcs = $questionTCS->getGroupeQuestion()->getQuestionnaireTCS();
        $logKey = sprintf("p_log_tcs_%s_%s", $participation->getId(), $questionnaireTcs->getId());
        $logAction = ParticipationLog::ACTION_FORM_PRE;

        $isLastSynthese =$questionTCS->getGroupeQuestion() === $questionnaireTcs->getGroupeQuestionsTCS()->last();

        $form = $this->createFormBuilder()
            ->getForm();
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            if ($isLastSynthese && !$participation->minModuleTimeReached($currentModule)) {
                $this->flashMessages->addError("front.error.minTime");
            } else {
                $this->createSyntheseEducativeComplete($participation, $questionTCS, $entityManager);
                return $this->redirectToRoute('app_admin_tcs_participation_next_question', [
                    'participation' => $participation->getId(),
                    'questionTCS' => $questionTCS->getId(),
                ]);
            }
        }

        $this->participationLogger->startLog($participation, $logAction, $logKey, $participation->isStepCompleted($currentModule) ? 100 : 0, $participation->isStepCompleted($currentModule) ? "100" : "0");

        return $this->render('audit/module/participation/synthese_educative.html.twig', [
            'questionnaireTCS' => $questionnaireTcs,
            'question' => $questionTCS,
            'participation' => $participation,
            'step' => $step,
            'course' => $course,
            'current_module' => $currentModule,
            'courseManager' => $this->courseManager,
            'form' => $form,
            'isLastSynthese' => $isLastSynthese
        ]);
    }

    public function canAccessToModules($participation) {
        if ($participation->getParticipant()->getUser() !== $this->getUser()) {
            return $this->courseManager->canAccessToModules($participation, $this->getUser());
        }
        return true;
    }

    private function createSyntheseEducativeComplete(Participation $participation, QuestionTCS $questionTCS, EntityManagerInterface $entityManager): void
    {
        $pgqTCS = new ParticipationGroupeQuestionTCS($participation, $questionTCS->getGroupeQuestion(), true);
        $entityManager->persist($pgqTCS);
        $entityManager->flush();
    }
}
