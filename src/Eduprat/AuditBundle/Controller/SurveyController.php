<?php

namespace Eduprat\AuditBundle\Controller;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\AuditBundle\Services\FormManagerFactory;
use Eduprat\AuditBundle\Services\ParticipationAccessManager;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\ParticipationLog;
use Eduprat\DomainBundle\Services\EmailSender;
use Eduprat\DomainBundle\Services\ParticipationLogger;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\Request;

class SurveyController extends EdupratController
{
    /**
     * @param Request $request
     * @param Participation $participation
     * @param                                            $surveyId
     * @return RedirectResponse|Response
     */
    #[Route(path: '/survey/{id}/{surveyId}', name: 'eduprat_survey_answer', requirements: ['surveyId' => '1|2'])]
    public function answer(Request $request, Participation $participation, string $surveyId, ParticipationAccessManager $accessManager, FormManagerFactory $formManagerFactory, EmailSender $emailSender, ParticipationLogger $participationLogger, CourseManager $courseManager)
    {
        $surveyId = (int) $surveyId;

        $currentModule = $surveyId === 1 ? CourseManager::STEP1_FORM_PRE_LABEL : CourseManager::STEP3_FORM_POST_LABEL;

        $this->denyAccessUnlessGranted('answer', $participation);
        if (!$this->isGranted('ROLE_COORDINATOR') && !$accessManager->canAnswerForm($participation, $surveyId)) {
            throw $this->createAccessDeniedException();
        }

        $surveyManager = $formManagerFactory->getSurveyManager($participation, $surveyId);

        $session = $request->getSession();

        $logKey = sprintf("p_log_survey_%s", $participation->getId());
        $logAction = $surveyId === 1 ? ParticipationLog::ACTION_FORM_PRE : ParticipationLog::ACTION_FORM_POST;
        $participationLogger->startLog($participation, $logAction, $logKey);

        $sessionKey = "participationDuration_" . $participation->getId();
        if(!$session->has($sessionKey)) {
            $session->set($sessionKey, time());
        }
        else {
            // $participationInterval = time() - $session->get($sessionKey);

            // if(is_null($participation->getTotalTime())) {
            //     $participationDuration = 0;
            // }
            // else {
            //     $participationDuration = $participation->getTotalTime();
            // }

            // $participationDuration += $participationInterval;
            // $participation->setTotalTime($participationDuration);
            $session->set($sessionKey, time());
        }

        if(is_null($participation->getStartedAt())) {
            $dateNow = new \Datetime();
            if($dateNow > $participation->getFormation()->getOpeningDate() && $dateNow < $participation->getFormation()->getStartDate()) {
                $participation->setStartedAt($dateNow);
            }
            else {
                $participation->setStartedAt($participation->getFormation()->getStartDate());
            }
        }
        if(is_null($participation->getIp())) {
            $participation->setIp($request->getClientIp());
        }

        $form = $surveyManager->getForm();

        $form->handleRequest($request);

        $completed = $surveyManager->isCompleted();

        if ($form->isSubmitted() && $form->isValid()) {
            if (!$participation->minModuleTimeReached($currentModule)) {
                $this->flashMessages->addError("front.error.minTime");
            } else {
                $surveyManager->save($form);
                $surveyManager = $formManagerFactory->getSurveyManager($participation, $surveyId, true);

                $participationLogger->endLog($participation, $logAction, $logKey, 100);

                if ($surveyManager->isLastSurvey()) {

                    $dateNow = new \Datetime();
                    $endDate = $participation->getFormation()->getEndDate()->add(new \DateInterval('P1D'));

                    if(($participation->getFormation()->isElearning() || $dateNow > $endDate) && $dateNow < $participation->getFormation()->getClosingDate()) {
                        $participation->setFinishedAt($dateNow);
                    }
                    else {
                        $participation->setFinishedAt($participation->getFormation()->getClosingDate());
                    }

                    // if($participation->getTotalTime() > $participation->getFinishedAt()->getTimestamp() - $participation->getStartedAt()->getTimestamp()) {
                    //     $additionalTime = 0;
                    //     // On ajoute 15 minutes pour la partie elearning des formations powerpoint
                    //     if ($participation->getFormation()->isPowerpoint()) {
                    //         $additionalTime = 15 * 60;
                    //     }
                    //     $participation->setTotalTime($participation->getFinishedAt()->getTimestamp() - $participation->getStartedAt()->getTimestamp() + $additionalTime);

                    //     // Sécurité si le temps de participation est trop court suite à correction
                    //     // A supprimer si client demande à réinstaurer un temps minimal de participation
                    //     if($participation->getTotalTime() <= 35 * 60) {
                    //         $participation->setTotalTime(rand(35, 45) * 60);
                    //     }
                    // }

                    // Temps de participation de 35 à 45mn si les audits sont validés trop rapidement
                    // A conserver au cas où client le demande
                    // if($participation->getTotalTime() <= 35 * 60) {
                    //     $participation->setTotalTime(rand(35, 45) * 60);
                    // }

                    $participation->setCompletedForm2(true);
                    $participation->setCompletedForms(true);

                    if (!$participation->isStepCompleted($currentModule)) {
                        $courseManager->completeCourseStep($participation, $currentModule);
                    }

                    $this->flashMessages->addSuccess('survey.success2', array("%title%" => $participation->getFormation()->getSurveyLabel($surveyId)));

                    if ($courseManager->isOldCourse($participation) && $accessManager->canEvaluateProgramme($participation->getFormation(), $participation->getParticipant()->getUser())) {
                        return $this->redirectToRoute('eduprat_evaluation_global', array('formation' => $participation->getFormation()->getId(), "person" => $participation->getParticipant()->getUser()->getId(), "page" => 1));
                    }

                } else {
                    $participation->setCompletedForm1(true);
                    if (!$participation->isStepCompleted($currentModule)) {
                        $courseManager->completeCourseStep($participation, $currentModule);
                    }
                    $this->flashMessages->addSuccess('survey.success', array("%title%" => $participation->getFormation()->getSurveyLabel($surveyId)));
                }

                $surveyManager->saveParticipation();

                $course = $courseManager->getCourseDetail($participation);

                if ($request->request->has("redirect") && $request->request->get("redirect") === "admin") {
                    return $this->redirectToRoute("admin_formation_show", array(
                        "id" => $participation->getFormation()->getId(),
                        "_fragment" => "parcours-" . $participation->getId()
                    ));
                }

                $nextModule = $courseManager->getNextModule($course, $currentModule);
                if ($nextModule && $nextModule["url"]) {
                    return $this->redirect($nextModule["url"]);
                }

                return $this->redirectToRoute('eduprat_audit_index');
            }
        }

        $course = $courseManager->getCourseDetail($participation);
        $step = $course[$surveyId === 1 ? CourseManager::STEP1 : CourseManager::STEP3];

        $choicePictures = [];
        foreach($surveyManager->getQuestions() as $question) {
            if ($question->getType() == "choice") {
                foreach($question->getChoices() as $choice) {
                    if ($choice->getPicture()) {
                        $choicePictures[$choice->getId()] = "/uploads/surveyChoicePicture/" . $choice->getPicture();
                    }
                }
            }
        }

        return $this->render('audit/survey/answer.html.twig', array(
            'participation' => $participation,
            'form' => $form,
            'completed' => $completed,
            'surveyLabel' => "",
            'surveyId' => $surveyId,
            'courseManager' => $courseManager,
            'course' => $course,
            'step' => $step,
            "current_module" => $currentModule,
            "choicePictures" => $choicePictures
        ));
    }

    /**
     * @param Request $request
     * @param Participation $participation
     * @param int                                        $surveyId
     * @return RedirectResponse|Response
     */
    #[Route(path: '/survey/show/{id}/{surveyId}', methods: ['GET'], name: 'eduprat_survey_show')]
    public function surveyShow(Participation $participation, CourseManager $courseManager, ParticipationLogger $participationLogger, $surveyId = 1): Response
    {
        $surveyId = (int) $surveyId;

        $currentModule = $surveyId === 1 ? CourseManager::STEP1_FORM_PRE_LABEL : CourseManager::STEP3_FORM_POST_LABEL;

        $this->denyAccessUnlessGranted('answer', $participation);

        $course = $courseManager->getCourseDetail($participation);
        $step = $course[$surveyId === 1 ? CourseManager::STEP1 : CourseManager::STEP3];

        $participationLogger->startLog($participation, $currentModule, null, 100);

        return $this->render('audit/survey/show.html.twig', array(
            'participation' => $participation,
            'surveyLabel' => "",
            'surveyId' => $surveyId,
            'courseManager' => $courseManager,
            'course' => $course,
            'step' => $step,
            "current_module" => $currentModule
        ));
    }

}
