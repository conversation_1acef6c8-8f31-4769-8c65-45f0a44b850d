<?php

namespace Eduprat\AuditBundle\Controller;

use Doctrine\Common\DataFixtures\Executor\ORMExecutor;
use Doctrine\Common\DataFixtures\Purger\ORMPurger;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\DataFixtures\PlaywrightFixtures;
use Eduprat\DomainBundle\DataFixtures\ResetAutoIncrementFixtures;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Attribute\Route;

#[Route(path: '/playwright')]
class PlaywrightController extends AbstractController
{
    #[Route(path: '/init', name: 'app_playwright_init')]
    public function init(
        ParameterBagInterface      $parameterBag,
        PlaywrightFixtures         $playwrightFixtures,
        ResetAutoIncrementFixtures $resetFixtures,
        EntityManagerInterface     $entityManager
    ): Response {
        if (!$parameterBag->get('playwrightEnv')) {
            throw new NotFoundHttpException();
        }

        $purger = new ORMPurger($entityManager);
        $executor = new ORMExecutor($entityManager, $purger);
        $executor->execute([$resetFixtures, $playwrightFixtures]);

        return new JsonResponse([
            'status' => 'OK',
        ]);
    }
}
