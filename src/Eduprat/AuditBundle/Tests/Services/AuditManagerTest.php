<?php

namespace Eduprat\DomainBundle\Tests\Services;

use Doctrine\ORM\EntityManager;
use Eduprat\AuditBundle\Services\AuditManager;
use Eduprat\DomainBundle\Entity\Audit;
use Eduprat\DomainBundle\Entity\FormationAudit;
use Eduprat\DomainBundle\Entity\Participation;

class AuditManagerTest extends \PHPUnit_Framework_TestCase
{
    /**
     * @var EntityManager
     */
    protected $em;

    /**
     * @var AuditManager
     */
    protected $auditManager;

    public function setUp() {
        $this->em = $this->getMock('\Doctrine\ORM\EntityManager', array('getRepository', 'getClassMetadata', 'persist', 'flush'), array(), '', false);
        $formFactory = $this->getMock('Symfony\Component\Form\FormFactory', array(), array(), '', false);

        $formation = new FormationAudit();
        $audit = new Audit();
        $participation = new Participation();

        $formation->setAudit($audit);
        $participation->setFormation($formation);

        $this->auditManager = new AuditManager($this->em, $formFactory, $participation, 1, 1);
    }

    public function testtruc() {
        $this->assertNotNull(null);
    }
}
