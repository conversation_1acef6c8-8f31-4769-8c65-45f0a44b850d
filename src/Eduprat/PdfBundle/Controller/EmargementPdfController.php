<?php

namespace Eduprat\PdfBundle\Controller;

use <PERSON><PERSON>rat\DomainBundle\Entity\FinanceSousMode;
use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Eduprat\PdfBundle\Services\EmargementPDF;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Service\Attribute\Required;

class EmargementPdfController extends AbstractPdfController
{
    private EmargementPDF $emargementPDF;

    #[Required]
    public function withEmargementPDF(EmargementPDF $emargementPDF): void
    {
        $this->emargementPDF = $emargementPDF;
    }

    #[Route(path: 'pdf/emargement/{id}/{financeSousMode}/pdf/{token}', name: 'pdf_emargement_pdf', methods: ['GET'])]
    #[Route(path: 'pdf/emargement_force/{id}/{financeSousMode}/pdf/{token}/{unity}', name: 'pdf_emargement_force_unity_pdf', methods: ['GET'])]
    public function emargement(Request $request, Formation $formation, FinanceSousMode $financeSousMode, $token = null, $unity = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageOffset = $request->query->get('offset');

        $unityPosition = $unity ?? -1;

        $filename = "emargement-";
        if ($unityPosition > 0) {
            $filename .= 'u'.$unityPosition."-";
        }
        $filename.= $formation->getProgramme()->getReference().".pdf";


        $response =  $this->emargementPDF->generate(
            ['formation' => $formation, 'unity' => $unity, 'financeSousMode' => $financeSousMode], null, ['pageOffset' => $pageOffset]
        );

        return new Response(
            $response->getBody(),
            Response::HTTP_OK,
            array(
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            )
        );
    }
}