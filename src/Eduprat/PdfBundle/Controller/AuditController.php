<?php

namespace Eduprat\PdfBundle\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Form\DownloadedPlaquetteFileType;
use Eduprat\AuditBundle\Services\AuditManager;
use Eduprat\AuditBundle\Services\FormManagerFactory;
use Eduprat\DomainBundle\Entity\DownloadedPlaquetteFile;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Entity\Formateur;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\FormationActalians;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Services\DownloadedPlaquetteFileManager;
use Eduprat\DomainBundle\Services\InvoiceManager;
use Eduprat\PdfBundle\Controller\AbstractPdfController;
use Eduprat\PdfBundle\Services\AuditPDF;
use Eduprat\PdfBundle\Services\CertificateParticipationPDF;
use Gotenberg\Exceptions\GotenbergApiErrored;
use Gotenberg\Gotenberg;
use Gotenberg\Stream;
use Knp\Snappy\Pdf;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Process\Process;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\Participant;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;

class AuditController extends AbstractPdfController
{
    #[Route(path: '/pdf/audit/{id}/{auditId}/pdf/{token}', name: 'pdf_audit_pdf', requirements: ['id' => '\d+', 'auditId' => '\d+', 'patient' => '\d+'], methods: ['GET'])]
    public function audit(Request $request, Participation $participation, AuditPDF $auditPDF, $auditId = 1, $token = null): Response
    {
        $this->denyAccessIfInvalidToken($participation, $token);
        $filename = "audit-".$auditId."-".$participation->getParticipant()->getFullname()."-".$participation->getFormation()->getProgramme()->getReference().".pdf";

        $response = $auditPDF->generate(
            ['participation' => $participation, 'auditId' => $auditId, 'request' => $request],
            ['formation' => $participation->getFormation()],
        );
        return new Response(
            $response->getBody(),
            Response::HTTP_OK,
            array(
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            )
        );
    }

    #[Route(path: '/pdf/audit/{id}/{auditId}/{patient}/pdf/{token}', name: 'pdf_audit_pdf_patient', requirements: ['id' => '\d+', 'auditId' => '\d+', 'patient' => '\d+'], methods: ['GET'])]
    public function auditPatient(Request $request, Participation $participation, AuditPDF $auditPDF, $patient, $auditId = 1, $token = null): Response
    {
        $this->denyAccessIfInvalidToken($participation, $token);

        $filename = "audit-".$auditId."-".$patient."-".$participation->getParticipant()->getFullname()."-".$participation->getFormation()->getProgramme()->getReference().".pdf";
        $response = $auditPDF->generate(
            ['participation' => $participation, 'auditId' => $auditId, 'patient' => $patient, 'request' => $request],
            ['formation' => $participation->getFormation()],
        );
        return new Response(
            $response->getBody(),
            Response::HTTP_OK,
            array(
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            )
        );
    }
}
