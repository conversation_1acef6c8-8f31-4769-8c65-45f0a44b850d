<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Form\DownloadedPlaquetteFileType;
use Ed<PERSON>rat\DomainBundle\Entity\DownloadedPlaquetteFile;
use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Services\DownloadedPlaquetteFileManager;
use Eduprat\PdfBundle\Controller\AbstractPdfController;
use Eduprat\PdfBundle\Controller\AbstractPlaquettePdfController;
use Eduprat\PdfBundle\DTO\ParamsPDFDTO;
use Eduprat\PdfBundle\Services\AbstractPDFGenerator;
use Eduprat\PdfBundle\Services\FlyerPdf;
use Eduprat\PdfBundle\Services\InvitationPdf;
use Eduprat\PdfBundle\Services\PdfSaver;
use Psr\Log\LoggerInterface;
use Symfony\Bridge\Doctrine\Attribute\MapEntity;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Contracts\Service\Attribute\Required;

class InvitationController extends AbstractPlaquettePdfController
{

    private InvitationPdf $invitationPdf;

    #[Required]
    public function withInvitationPdf(InvitationPdf $invitationPdf): void
    {
        $this->invitationPdf = $invitationPdf;
    }

    #[Route(path: '/pdf/invitation/{user}/{id}/pdf/{tokenUser}/{token}', name: 'pdf_invitation_pdf', methods: ['POST'])]
    public function invitation(Request $request, DownloadedPlaquetteFileManager $downloadedPlaquetteFileManager, Person $user, Formation $formation, $tokenUser = null, $token = null): JsonResponse
    {
        $this->denyAccessIfInvalidToken($formation, $token);
        $this->denyAccessIfInvalidToken($user, $tokenUser);

        $downloadedPlaquetteFile = new DownloadedPlaquetteFile();
        $downloadedPlaquetteFile->setType(DownloadedPlaquetteFile::TYPE_INVITATION);
        $download_plaquette_file_form = $this->createForm(DownloadedPlaquetteFileType::class, $downloadedPlaquetteFile);
        $download_plaquette_file_form->handleRequest($request);

        // si formulaire non valide ou non soumis
        if (!($download_plaquette_file_form->isSubmitted() && $download_plaquette_file_form->isValid())) {
            return new JsonResponse(['etat' => false]);
        }
        $downloadedPlaquetteFile->setPerson($user);

        $filename = "invitation-" . $formation->getProgramme()->getReference() .'-s' . $formation->getSessionNumber() .'-'.uniqid().".pdf";
        $paramsPDFDTO = new ParamsPDFDTO(
            [
                'titre' => $downloadedPlaquetteFile->getTitre(),
                'formation' => $formation,
            ],
            null,
            [
                'coordinateur' => $user,
            ]
        );

        return $this->generatePdfAndResponseLocal($downloadedPlaquetteFile, $paramsPDFDTO, $user, $downloadedPlaquetteFileManager, $filename);
    }

    public function getPdfService(): AbstractPDFGenerator
    {
        return $this->invitationPdf;
    }
}
