<?php

namespace Eduprat\PdfBundle\Services;

use Ed<PERSON>rat\AuditBundle\Form\SurveyType;
use Ed<PERSON>rat\DomainBundle\Entity\FormationPresentielle;
use Ed<PERSON>rat\DomainBundle\Entity\Participation;
use Psr\Http\Message\ResponseInterface;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Contracts\Service\Attribute\Required;

class SurveyEmptyPDF extends AbstractPDFGenerator
{
    private FormFactoryInterface $formFactory;

    #[Required]
    public function withFormFactory(FormFactoryInterface $formFactory): void
    {
        $this->formFactory = $formFactory;
    }

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string
    {
        /** @var FormationPresentielle $formation */
        $formation = $params['formation'] ?? throw new \LogicException('parameter formation not found');
        if (!$formation instanceof FormationPresentielle) {
            throw new \LogicException('parameter formation must be a FormationPresentielle');
        }
        $surveyId = $params['surveyId'] ?? throw new \LogicException('parameter surveyId not found');
        $paramsHeader['formation'] = $formation;
        return $this->build($this->generateContent($formation, $surveyId), $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [1.5, 1, 0, 0];
    }

    public function getHeaderTwigPath(): string
    {
        return 'pdf/headers/gotenberg-evaluation-global.html.twig';
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }

    private function generateContent(FormationPresentielle $formation, int $surveyId): string
    {
        $questions = $formation->getQuestionnaire()->getQuestions();
        $questions->forAll(function($key, $question) {
            $question->setIndex($key);
            return true;
        });

        $form = $this->formFactory->create(SurveyType::class, [], array(
            "questions" => $questions
        ));

        $participation = new Participation();
        $participation->setFormation($formation);

        return $this->twig->render('pdf/survey.html.twig', array(
            "participation" => $participation,
            'form' => $form->createView(),
            'surveyLabel' => $formation->getQuestionnaireLabel1(),
            'surveyId' => $surveyId
        ));
    }
}
