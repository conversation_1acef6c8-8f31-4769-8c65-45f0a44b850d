<?php

namespace Eduprat\PdfBundle\Services;

use Psr\Http\Message\ResponseInterface;
class CertificateParticipationPDF extends AbstractPDFGenerator
{
    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string
    {
        $participation = $params['participation'] ?? throw new \LogicException('parameter participation not found');

        $template = $participation->getFinanceSousMode()->isActalians() ? 'certificate_participation_actalians' : 'certificate_participation';
        $content = $this->twig->render('pdf/' . $template . '.html.twig', array(
            'participation' => $participation,
            'participant' => $participation->getParticipant()
        ));

        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [1.5, 1, 0, 0];
    }

    public function getHeaderTwigPath(): string
    {
        return 'pdf/headers/gotenberg-evaluation-global.html.twig';
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }
}