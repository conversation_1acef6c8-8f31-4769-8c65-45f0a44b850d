<?php

namespace Eduprat\PdfBundle\Services;

use Doctrine\Common\Collections\ArrayCollection;
use Twig\Environment;

class EmargementPDF extends AbstractPDFGenerator
{

    private $header = "pdf/headers/gotenberg-dpc.html.twig";
    private $footer = 'pdf/footers/gotenberg-evaluation-global.html.twig';

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): \Psr\Http\Message\ResponseInterface|string
    {
        $formation = $params['formation'] ?? throw new \LogicException('parameter formation not found');
        $unity = $params['unity'] ?? null;
        $financeSousMode = $params['financeSousMode'] ?? throw new \LogicException('parameter financeSousMode not found');

        $participations = $formation->getParticipationsPerMode($financeSousMode);
        $iterator = $participations->getIterator();
        $iterator->uasort(function ($a, $b) {
            return (ucfirst(strtolower($a->getParticipant()->getLastname())) < ucfirst(strtolower($b->getParticipant()->getLastname()))) ? -1 : 1;
        });
        $participations = new ArrayCollection(iterator_to_array($iterator));

        $formateurs = $formation->getFormateurs();
        $template = $financeSousMode->isHorsDPC() || $financeSousMode->isFif() || $financeSousMode->isActalians() ? 'emargement_hors_dpc' : 'emargement';

        $content = [];
        $unityPosition = $unity ?? -1;
        
        if ($formation->isFourUnity() && !$formation->getProgramme()->isClasseVirtuelle()) {

            $content[] = $this->twig->render('pdf/' . $template . '.html.twig', array(
                'participations' => $participations,
                'formation' => $formation,
                'formateurs' => $formateurs,
                'date' => "",
                'unityPosition' => 2,
            ));

            $content[] = $this->twig->render('pdf/' . $template . '.html.twig', array(
                'participations' => $participations,
                'formation' => $formation,
                'formateurs' => $formateurs,
                'date' => "",
                'unityPosition' => 3,
            ));
        } else {
            $unity = $formation->getUnityByPosition($unity ?? $formation->getProgramme()->getReunionUnityPosition());
            if ($unity) {
                foreach ($unity->getUnitySessionDates() as $unityDate){
                    $content[] = $this->twig->render('pdf/' . $template . '.html.twig', array(
                        'participations' => $participations,
                        'formation' => $formation,
                        'formateurs' => $formateurs,
                        'date' =>  $unityDate->getStartDate()->format("d/m/Y"),
                        'unityPosition' => $unityPosition,
                    ));
                }
            } else {
                $content[] = $this->twig->render('pdf/' . $template . '.html.twig', array(
                    'participations' => $participations,
                    'formation' => $formation,
                    'formateurs' => $formateurs,
                    'date' =>  '',
                    'unityPosition' => null,
                ));
            }
        }

        if (count($content) > 1) {
            $content = implode('', $content);
        } else {
            $content = $content[0];
        }

        $paramsHeader['title'] = 'Emargements (par demi-journées ou soirées)';
        $paramsHeader['height'] = '4.7cm';

        if ($financeSousMode->isHorsDPC() || $financeSousMode->isFif() || $financeSousMode->isActalians()) {
            $this->header = "pdf/headers/gotenberg-hors_dpc.html.twig";
        } else {
            $this->footer = 'pdf/footers/gotenberg-dpc.html.twig';
        }


        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [2, 1.2, 0, 0];
    }

    public function getHeaderTwigPath(): string
    {
        return $this->header;
    }

    public function getFooterTwigPath(): string
    {
        return $this->footer;
    }
}